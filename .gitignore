# Hugo generated files
public/
resources/_gen/

# <PERSON> cache
.hugo_build.lock

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor files
*.swp
*.swo
*~
.vscode/
.idea/

# Log files
*.log

# Temporary files
*.tmp
*.temp

# Node modules (if using npm/yarn for asset processing)
node_modules/

# Environment files
.env
.env.local
.env.production

# Hugo lock file
package-lock.json
yarn.lock
