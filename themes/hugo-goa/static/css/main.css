@-ms-viewport {
  width: device-width;
}
@-o-viewport {
  width: device-width;
}
@viewport {
  width: device-width;
}
a,
a:hover,
a:visited,
a:link {
  text-decoration: none;
  color: rgb(24 24 27);
}
body {
  font-family: "Roboto Slab", "Roboto", "Lato", sans-serif;
  font-size: 20px;
  font-weight: 400;

  width: auto;
  height: 100%;
  min-height: 100%;
  margin: 0;

  background-color: #fcfcfc;
}
html {
  position: relative;

  height: 100%;
  min-height: 100%;
  margin: 0;
}
li {
  text-align: left;
}
.author {
  color: #444;
}
.author h1,
.author h2,
.author h3,
.author h4,
.author h5,
.author h6,
.author p {
  font-family: "Roboto Slab", serif;
}
.category {
  color: #966 !important;
}
.container {
  overflow: auto;
  max-width: 800px;
  padding: 0 15px;
}
.content {
  margin-top: 20px;
  margin-bottom: 20px;
}
.content a {
  text-decoration: none;
  color: rgb(153 27 27);
}
div.content {
  font-family: "Roboto", sans-serif;
  font-size: 18px;
  font-weight: 300;

  color: #777;
}
.copyright {
  font-family: "Roboto", sans-serif;
  font-size: 12px;
  font-weight: 200;
}
.description {
  font-family: "Roboto", sans-serif;
  font-weight: 200;
  color: rgb(161 161 170);
  font-size: 16px;
}
.error {
  font-family: "Roboto", sans-serif;
  font-weight: 300;
  margin-top: 100px;
}
.error a {
  color: rgb(153 27 27);
}
.error h1 {
  font-size: 80px;

  color: #8b8b8b;
}
.error p {
  margin-top: 150px;

  color: #777;
}
.excerpt {
  font-family: "Lato", sans-serif;
  font-size: 18px;
  font-weight: 200;

  color: #555;
  margin-bottom: 20px;
}
.footer {
  margin-top: 10px;

  color: #8b8b8b;
}
.header {
  margin-top: 60px;
  margin-bottom: 20px;
}
.headshot {
  width: 180px;
  height: 180px;
  object-fit: cover;
}
.info {
  margin-top: 10px;
  margin-bottom: 20px;
}
.intro {
  font-family: "Roboto Slab", serif;
  font-weight: 300;
  font-size: 20px;
  color: rgb(82 82 91);
}
h3.intro {
  margin-top: 0px;
}
.list {
  font-family: "Roboto", sans-serif;

  margin-top: 50px;
}
.list li {
  list-style: none;
}
.list-entry {
  font-family: "Roboto Slab", serif;
  font-size: 25px;
  font-weight: 400;

  margin-top: 10px;
  margin-bottom: 20px;
}
.list-entry-link {
  padding: 0;
}
.main-menu {
  margin-top: 10px;
  margin-bottom: 20px;
}
.menu {
  margin-top: 10px;
  margin-bottom: 20px;
}
.menu-item {
  font-family: "Lato", sans-serif;
  font-size: 20px;
  font-weight: 300;

  text-decoration: none;

  color: rgb(153 27 27) !important;
}
.meta {
  font-family: "Roboto", sans-serif;
  font-size: 12px;
  font-weight: 200;
  color: rgb(113 113 122);
}
a.meta {
  color: rgb(153 27 27);
}
h6.meta {
  font-family: "Roboto", sans-serif;
  font-size: 12px;
  font-weight: 200;
  color: rgb(113 113 122);
}
section.meta {
  margin-top: ;
}
.powered {
  font-family: "Roboto", sans-serif;
  font-size: 12px;
  font-weight: 300;
}
.social {
  margin: 20px 10px;
}
.social a {
  color: #526d7a;
  margin: 5px;
}
.title {
  font-family: "Roboto Slab", serif;
  font-weight: 400;

  rgb(24 24 27);
}
header.title {
  margin-top: 50px;
  padding: 0px;
}
@media (max-width: 991px) {
  .header {
    margin-top: 50px;
    margin-bottom: 20px;
  }
  .list {
    font-family: "Roboto", sans-serif;

    margin-top: 30px;
  }
}
pre > code {
  white-space: pre;
  word-wrap: initial;
  word-break: initial;
}
