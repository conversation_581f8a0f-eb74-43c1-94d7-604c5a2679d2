<!DOCTYPE html>
<html lang="{{ .Site.LanguageCode }}">

    <head>

        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="content-type" content="text/html; charset=utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">

        {{ with .Site.Params.meta.description }}
        <meta name="description" content="{{ . }}">{{ end }}
        {{ with .Site.Params.meta.keywords }}
        <meta name="keywords" content="{{.}}">{{ end }}

        <title>
            {{ $url := replace .Permalink ( printf "%s" .Site.BaseURL) "" }} {{ if eq $url "" }} {{ .Site.Title }}
            {{ else }} {{ .Site.Title }} - {{ .Title }} {{ end }}
        </title>

        {{ hugo.Generator }}

        {{ range .AlternativeOutputFormats -}}
        <link rel="{{ .Rel }}" type="{{ .MediaType.Type }}" href="{{ .Permalink | safeURL }}">
        {{ end -}}

        {{ if .Site.Params.extra.highlightjs }}
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/default.min.css">
        {{ end }}

        <link rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400&family=Roboto+Slab:wght@400;700&family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700" />
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
            integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC"
            crossorigin="anonymous" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
        <link rel="stylesheet" href="{{ "css/main.css" | absURL }}">
        <link rel="stylesheet" href="{{ "css/custom.css" | absURL }}">

        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
        <link rel="manifest" href="/site.webmanifest">
        <meta name="theme-color" content="#ffffff">
        {{- with .OutputFormats.Get "RSS" -}}
        <link rel="alternate" type="application/rss+xml" title="{{ $.Site.Title }}" href="{{ .RelPermalink }}" />
        <link rel="alternate" type="application/json" title="{{ $.Site.Title }}" href="{{ "feed.json" | absURL }}" />
        <link rel="EditURI" type="application/rsd+xml" href="{{ "rsd.xml" | absURL }}" />
        {{- end -}}

    </head>

    <body lang="{{ .Site.LanguageCode }}">
        <div class="container my-auto">