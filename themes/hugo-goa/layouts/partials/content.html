<header class="text-start title">
  <h1 class="title">{{ .Title }}</h1>
</header>

<section id="category-pane" class="meta">
  {{ if ne .Params.showpagemeta false }}
  <div class="d-flex flex-column flex-md-row justify-content-between">
    <h6 class="text-start meta">
      {{ if not .Date.IsZero }} PUBLISHED ON {{ .Date.Format (default "Jan 2, 2006" .Site.Params.dateformat) | upper }}
      {{ end }}
      / {{ math.Round (div (countwords .Content) 220.0) }} MIN READ
      {{ if isset .Params "categories" }}
      {{ $total := len .Params.categories }}
      {{ if gt $total 0 }}
      —
      {{ $total := sub $total 1 }}
      {{ range $i, $cat := sort $.Params.categories }}
      <a class="meta"
        href="{{ "categories/" | relURL }}{{ $cat | urlize }}">{{ $cat | upper }}</a>{{ if lt $i $total }}, {{ end }}
      {{ end }}
      {{ end }}
      {{ end }}
    </h6>
  </div>
  {{ end }}
</section>

<section id="content-pane">
  <div class="text-justify content">
    {{ if and (ne .Params.toc false) (gt .WordCount 300) }}
    {{ .TableOfContents }}
    {{ end }}
    {{ .Content }}
  </div>
</section>

<!-- CSDN链接部分 -->
{{ if .Params.csdn_url }}
<section id="csdn-link-pane" class="csdn-external-link">
  <div class="csdn-link-container">
    <div class="csdn-link-header">
      <i class="fas fa-external-link-alt"></i>
      <h6>在CSDN上阅读完整版</h6>
    </div>
    <div class="csdn-link-content">
      <p>本文同时发布在CSDN博客，您可以在那里查看更多技术细节和讨论。</p>
      <a href="{{ .Params.csdn_url }}" target="_blank" class="csdn-link-button">
        <i class="fab fa-blogger"></i>
        <span>访问CSDN文章</span>
        <i class="fas fa-arrow-right"></i>
      </a>
    </div>
  </div>
</section>
{{ end }}

<section id="tag-pane" class="meta">
  {{ if ne .Params.showpagemeta false }}
  <div class="d-flex flex-column flex-md-row justify-content-between">
    <h6 class="text-end meta">
      {{ if isset .Params "tags" }}
      {{ $total := len .Params.tags }}
      {{ if gt $total 0 }}
      TAGS:
      {{ $subtotal := sub $total 1 }}
      {{ range $i, $tag := sort $.Params.tags }}
      <a class="meta" href="{{ "tags/" | relURL }}{{ $tag | urlize }}">{{ $tag | upper }}</a>{{ if lt $i $subtotal }},
      {{ end }}
      {{ end }}
      {{ end }}
      {{ end }}
    </h6>
  </div>
  {{ end }}
</section>