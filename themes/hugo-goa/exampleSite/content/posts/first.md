+++
title = 'My First Post'
date = 2024-01-14T07:07:07+01:00
draft = false
+++

## Introduction

Welcome to my first post using <PERSON>! In this post, I'm going to showcase some basic Markdown formatting and discuss why I chose <PERSON> for my blogging platform.

This is **bold** text, and this is _emphasized_ text.

## Why <PERSON>?

[<PERSON>](https://gohugo.io) is a fast and flexible static site generator, perfect for blogs, portfolios, and even company websites. Here's why I love it:

- **Speed**: <PERSON> generates pages at an incredibly fast speed.
- **Flexibility**: You can create any type of website with <PERSON>.
- **Community**: The Hugo community is supportive and continuously growing.

## Markdown Basics

Markdown is a lightweight markup language that you can use to add formatting elements to plaintext text documents. Here are some basics:

- **Headers**: Use `##` for a secondary header, and `###` for a tertiary header.
- **Lists**:
  - Unordered lists are created using dashes (`-`).
  - Ordered lists simply use numbers.
- **Links**: To add a [link](#), put the text in square brackets and the URL in parentheses.
- **Images**: Add images with `![Alt text](image-url.jpg)`.
- **Code**: Use backticks to `highlight` inline code or triple backticks for code blocks.

## Conclusion

I hope this post gives you a glimpse into what <PERSON> and <PERSON>down can do. Stay tuned for more content!

Visit the [<PERSON>](https://gohugo.io) website to learn more!
