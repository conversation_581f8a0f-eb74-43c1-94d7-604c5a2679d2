name = "Goa"
license = "MIT"
licenselink = "https://github.com/shenoydotme/hugo-goa/blob/master/LICENSE.md"
description = "Simple Minimalist Hugo Theme"
homepage = "https://github.com/shenoydotme/hugo-goa"
demosite = "https://shenoydotme.github.io/hugo-goa-demo/"
tags = [
  "Roboto",
  "Lato",
  "Roboto Slab",
  "bootstrap",
  "font awesome",
  "minimal",
  "clean",
]
features = [
  "blog",
  "pages",
  "minimal",
  "responsive",
  "404",
  "icons",
  "disqus",
  "anlaytics",
]

min_version = "v0.134.0"

# If the theme has multiple authors
authors = [
  {name = "<PERSON><PERSON>", homepage = "https://shenoy.me"},
]