+++
title = 'LLM开发之Langchain'
date = 2024-12-15T11:00:00+08:00
draft = false
categories = ['LLM', 'Langchain']
tags = ['AI', 'langchain', '框架']

<!-- description = "机器学习的基础概念和入门指南" -->
+++

# 一、框架简述

LangChain 是一个用于构建和管理 LLM 应用的开发框架。它为开发者提供了工具和接口，以便于更轻松地将大语言模型集成到应用程序中，并处理语言模型生成的响应、管理对话状态、执行链式调用、处理多步任务等。

# 二、LangChain主要模块

## 1. Models--提供各种AI"大脑"

Models 主要分为 Language 和 TextEmbedding 两大模块，分别处理语言生成和语义表示。

### Language
- **LLM**：主要用于生成一般文本，接受输入后输出结果。常用于回答问题、生成段落、翻译等单一性任务，具有通用的文本生成能力。
- **Chat**：这是 LLM 的一种变体，专为对话设计，能够更自然地处理多轮对话。这类模型不仅接收输入并生成输出，还能记忆上下文，使得交互更加人性化。

### TextEmbedding
将输入文本（如单词、句子或段落）转换成多维向量（如图中显示的 0.21，0.57，0.14，0.37 等数值）。这种数值表示可以应用于多种场景，如相似性匹配、聚类、信息检索等。

- **特点**：把文本映射到向量空间中，使得系统能够量化文本之间的相似性，从而实现知识库的功能。嵌入向量越相近，代表文本的语义越相似。

## 2. Chains--"流程设计师"设计工作流程

由于单个任务可能无法完成多步骤或复杂任务，Chain 就提供了这个框架解决这个问题。Chain 是连接组件、管理组件数据流的“包装器”。基于链式调用，LangChain 可以将其他模块组合起来，以实现复杂的多步操作。适用复杂任务，不适用小任务。

## 3. Memory--"档案管理员"

Memory 是一种存储数据的方法，使得大型语言模型可以在后续的交互中访问该数据。这种数据可以包括之前 Chain 执行的结果、当前对话的上下文信息以及 LLM 需要的任何其他信息。借助记忆模块，应用程序能够在对话过程中跟踪当前上下文，实现更连续和智能的交互。

- **存储对话上下文**：在多轮对话中，记忆模块保存上下文信息，使 LLM 能够理解用户的连续提问，确保对话的连贯性。
- **积累知识库**：通过保存先前的链条结果，记忆模块能够建立一个知识库，使 LLM 能够从过去的交互中学习，从而在相似话题上提供更优的回答。
- **改进任务表现**：当应用需要回答某个领域的复杂问题时（例如房地产问题），记忆模块可以保留之前的答案或信息，从而在新问题出现时提供更高效、相关性更强的回答。

## 4. Tools--"工程师团队"开发业务功能

Tools 是功能库，支持代理完成具体的任务。每种 Tools 都是为特定任务设计的，比如数据处理、计算或信息检索。

- **DataManipulator tool**：用于处理数据的工具，支持清洗、转换或提取特征等操作，以便代理能够处理和分析数据。
- **Search tool**：用于在网络或数据库中查找信息，支持代理获取实时数据或现有知识库中的内容。

## 5. Agents--"项目经理"实现智能决策

Agents 是可以复用的组件，专门用于完成特定任务。能够自主判断所需的工具组合，因此具备一定的任务分解和执行能力。

- **NewsGeneratoragent**：用于生成新闻文章或标题的代理，能够根据输入内容生成符合新闻写作风格的文本。
- **QuestionAnsweringagent**：用于回答特定领域问题的代理，可以从数据库或网络中检索相关信息，提供准确的答案。

## 6. Prompts--专业"沟通专家" 教 AI 如何理解任务

Prompts 是用于引导大型语言模型生成所需输出的文本片段。提示的设计可以简单或复杂，目的是通过提供特定的指令或上下文来精确控制模型的输出。广泛应用于文本生成、语言翻译、问答等任务。

## 7. Indexes--"知识管理员"处理知识库

Indexes 是用来存储数据内容信息的独特数据结构，方便在数据集中快速找到相关内容。索引可以包含文档中的关键词、文档在数据集中的位置、文档之间的关系等信息。索引模块的核心组件包括 向量存储 (vectorstore) 和 检索器 (retriever)，用于实现数据的高效查找和检索。

### 向量存储 (vectorstore)
向量存储是一种数据结构，将数据集中词语的向量表示（如语义向量）存储起来。通过向量存储，系统可以根据查询内容在向量空间中找到语义相似的内容，比如“精灵”在《指环王》文档中被描述时的语义向量。

### 检索器 (retriever)
检索器是一个接口，用于响应非结构化查询并返回最相关的文档。相比向量存储，检索器范围更广，可以利用多个索引（如关键词索引、关系索引）来找到最符合用户需求的文档。

## 目标

- 了解 LangChain 的核心概念，深入学习其核心组件。
- 能独立用 LangChain 接 API、处理数据、搭出可用的 AI 工具。
- 了解 Llamaindex 的基本概念和使用模式，尝试搭建一个简单的文档问答系统。
- 能独立设计一个能自动完成任务的 Agent。
- 了解 GPTs、Coze、Dify 这 3 个框架的特点，使用它们搭建个 AI 应用。