+++
title = 'LLM 微调与对齐'
date = 2024-02-01T04:55:59+05:30
draft = false
categories = ['微调', '对齐']
<!-- tags = ['天文学', '宇宙', '环保', '卡尔萨根'] -->
description = "大模型应用开发之微调与对齐"
csdn_url = "https://blog.csdn.net/weixin_46516647/article/details/147539400"
+++

# 一、指令微调 Instruction Tuning

## （一）指令数据

大模型如同天赋异禀的学生，预训练阶段通过大量阅读积累知识，但缺乏交流和答题能力，此时指令数据便充当“教材和老师”的角色。

### 1. 指令数据组成

  * **指令 (Instruction / Prompt)** ：清晰告知模型任务，如 “请回答法国的首都是哪里？”。
  * **可选的示例 (Demonstration / Example Input)** ：提供输入样例助模型理解格式，如问题 “巴西的首都是哪里？”。
  * **输入 (Input)** ：当前任务的具体输入，如 “中国的首都是哪里？”。
  * **输出 (Output)** ：模型应给出的正确答案或完成内容，如 “北京”。

### 2. 指令数据构建方法

  * **基于现有 NLP 任务数据集** ：利用学术界的高质量现成数据集，如机器翻译、文本摘要、文本分类等，为输入 - 输出对配上明确指令（PromptSource 平台是贡献和使用指令模板的平台）。
  * **基于对话数据构建** ：收集用户查询，由人类标注员编写高质量回答或从模型生成回答中挑选最佳，组成训练数据。
  * **基于合成数据构建** ：
    * **Self-Instruct - “自己出题自己做”** ：准备少量种子指令，让种子模型生成更多指令及对应的输入和输出，过滤低质样本。
    * **Evol-Instruct - “指令进化论”** ：通过 “演化提示” 改写已有指令，让模型生成对应输出。

### 3. 指令数据构建考虑因素

  * **指令格式**
  * **指令数量**
  * **指令数据质量**
  * **任务多样性**

## （二）指令微调训练策略

### 1. 训练参数优化

指令微调中的优化器设置、稳定训练技巧和训练技术与预训练阶段多数保持一致，不同之处如下：

  * **目标函数** ：作为有监督训练过程，通常采用序列到序列损失，仅计算输出部分损失。
  * **批次大小和学习率** ：此阶段使用较小批次大小和学习率对模型进行小幅调整。
  * **多轮对话高效训练** ：一次性将多轮对话内容输入模型，通过损失掩码实现仅对每轮对话输出部分计算损失，减少重复前缀计算开销。

### 2. 指令数据规划

  * **平衡数据分布** ：混合使用多个不同来源、类型的指令数据集。
  * **多阶段指令数据微调** ：先用大量通用 NLP 任务指令数据微调打基础，再用少量高质量对话指令数据进一步微调提升能力。
  * **结合预训练数据与指令微调数据** ：在指令微调阶段混合预训练数据，保持通用知识和语言建模能力，防止性能退化。

## （三）参数高效微调 / 轻量化微调 (PEFT)

在微调时冻结大部分预训练模型参数，仅训练少量新增或选择参数，降低成本。

### 1. 低秩适配微调方法 (Low-Rank Adaptation, LoRA)

核心思想：大语言模型适应新任务时参数改变量具低秩特性，可用两小矩阵相乘近似表示。

### 2. 其他高效微调

  * **适配器微调 (Adapter Tuning)** ：在 Transformer 模型各层插入小型可训练神经网络模块（适配器），微调时只训练适配器参数。
  * **前缀微调 (Prefix Tuning)** ：在输入序列前添加可训练向量序列（前缀），指导模型处理后续输入完成任务。
  * **提示微调 (Prompt Tuning / P-Tuning)** ：在输入前添加可学习提示，微调时只更新提示向量参数。

# 二、人类对齐

大语言模型预训练和指令微调过程主要依据上下文预测词元，未充分考虑人类价值观和偏好，可能导致不良生成模式，人类对齐关键概念应运而生，旨在使模型行为契合人类期望和价值观。

## （一）对齐标准

引入全新评估标准，区别于预训练和指令微调。

## （二）基于人类反馈的强化学习 RLHF

鉴于对齐标准难形式化建模，研究提出 RLHF，引入人类反馈指导模型行为。

### 1. 监督微调

  * **收集数据** ：收集 “指令 - 优质回答” 高质量数据对。
  * **微调模型** ：用数据对预训练模型微调，类似指令微调，使模型学习模仿优质回答。

### 2. 奖励模型训练

  * **生成回答** ：SFT 模型针对同一指令生成多个不同回答。
  * **人类排序** ：标注员对回答排序，体现偏好，据此训练奖励模型。

### 3. 强化学习训练

基于训练好的奖励模型，通过强化学习优化语言模型，使其生成符合人类偏好的回答。

## （三）非强化学习的对齐方法

回归监督微调，关键在构建高质量对齐数据集和设计监督微调对齐算法。

### 1. 对齐数据的收集

  * **基于奖励模型的方法** ：用训练好的奖励模型为大量模型输出打分，筛选优质数据。
  * **基于大语言模型的方法** ：利用对齐较好或能力强的大语言模型生成或评价数据。

### 2. 监督对齐算法 DPO (Direct Preference Optimization)

  * **动机** ：简化 RLHF 过程，直接从人类偏好数据学习，无需训练奖励模型和强化学习。
  * **核心思想** ：合并 “学习奖励模型” 和 “优化策略模型” 步骤，形成单一监督学习损失函数，优化语言模型生成偏好回答。
  * **与 RLHF 的关系** ：DPO 是 RLHF 目标等价的直接优化形式，利用强化学习理论推导出可直接用偏好数据训练的损失函数。