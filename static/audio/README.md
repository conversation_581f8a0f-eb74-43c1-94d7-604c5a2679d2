# 音频文件目录

这个目录用于存放音乐作品的音频文件。

## 文件命名规范

- `song1.mp3` - 第一首歌的音频文件
- `song2.mp3` - 第二首歌的音频文件
- `song3.mp3` - 第三首歌的音频文件

## 音频要求

- 格式：MP3、WAV 或 OGG
- 质量：建议 128kbps 或更高
- 文件大小：建议小于 10MB

## 重要说明

⚠️ **版权注意事项**：
- 只能上传您拥有版权的原创音乐作品
- 不要上传受版权保护的他人作品
- 确保您有权在网站上分享这些音频文件

## 替代方案

如果您不想直接上传音频文件，可以：

1. **使用网易云音乐外链播放器**：
   - 获取您在网易云音乐上的歌单ID
   - 在 `content/Music/index.md` 中更新 iframe 的 src 属性

2. **链接到外部音乐平台**：
   - 直接链接到网易云音乐、QQ音乐等平台的歌曲页面
   - 用户点击后跳转到对应平台播放

## 使用说明

1. 将您的音频文件放在这个目录中
2. 按照命名规范重命名文件
3. 在 `themes/hugo-goa/static/js/custom.js` 中更新 musicData 对象中的 audioUrl 路径
