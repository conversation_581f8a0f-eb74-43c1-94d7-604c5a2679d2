<svg width="300" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="300" height="300" fill="url(#grad1)"/>
  <circle cx="150" cy="120" r="40" fill="white" opacity="0.8"/>
  <polygon points="135,105 135,135 165,120" fill="#667eea"/>
  <text x="150" y="180" font-family="Arial, sans-serif" font-size="16" fill="white" text-anchor="middle">音乐封面</text>
  <text x="150" y="200" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle" opacity="0.8">Music Cover</text>
</svg>
