# 音乐页面设置指南

这个指南将帮助您完成音乐页面的设置，让您能够在网站上展示和播放您的音乐作品。

## 🎵 功能特性

- ✅ 响应式音乐播放器界面
- ✅ 音乐作品展示卡片
- ✅ 网易云音乐外链播放器集成
- ✅ 本地音频文件播放支持
- ✅ 移动端适配

## 📋 设置步骤

### 1. 准备音乐资源

#### 音频文件（可选）
如果您想要本地播放功能：
- 将音频文件（MP3格式）放入 `static/audio/` 目录
- 文件命名：`song1.mp3`, `song2.mp3`, `song3.mp3` 等

#### 封面图片
- 将封面图片放入 `static/images/music/` 目录
- 文件命名：`cover1.jpg`, `cover2.jpg`, `cover3.jpg` 等
- 建议尺寸：300x300 像素

### 2. 获取网易云音乐信息

#### 获取歌曲ID
1. 打开网易云音乐网页版
2. 找到您的歌曲页面
3. 从URL中复制歌曲ID
   - 例如：`https://music.163.com/#/song?id=123456789`
   - 歌曲ID就是：`123456789`

#### 获取歌单ID（用于外链播放器）
1. 创建包含您所有作品的歌单
2. 从歌单URL中复制ID
   - 例如：`https://music.163.com/#/playlist?id=987654321`
   - 歌单ID就是：`987654321`

### 3. 更新配置文件

#### 更新音乐信息
编辑 `content/Music/index.md`，替换以下内容：

```markdown
<!-- 在每个音乐作品的 netease-link 中 -->
<a href="https://music.163.com/#/song?id=YOUR_SONG_ID_1" target="_blank" class="netease-link">

<!-- 在网易云音乐播放器的 iframe 中 -->
<iframe src="//music.163.com/outchain/player?type=0&id=YOUR_PLAYLIST_ID&auto=0&height=430">
```

将 `YOUR_SONG_ID_1`, `YOUR_SONG_ID_2` 等替换为实际的歌曲ID
将 `YOUR_PLAYLIST_ID` 替换为您的歌单ID

#### 更新个人信息
在 `content/Music/index.md` 中更新：
- 歌曲标题和描述
- 艺术家名称
- 发布日期
- 歌曲时长
- 网易云音乐个人主页链接

### 4. 网易云音乐外链播放器参数说明

iframe URL 参数：
- `type=0`: 歌单
- `type=1`: 单曲
- `type=2`: 专辑
- `id=`: 对应的ID
- `auto=0`: 不自动播放
- `auto=1`: 自动播放
- `height=`: 播放器高度

示例：
```html
<!-- 歌单播放器 -->
<iframe src="//music.163.com/outchain/player?type=0&id=歌单ID&auto=0&height=430"></iframe>

<!-- 单曲播放器 -->
<iframe src="//music.163.com/outchain/player?type=1&id=歌曲ID&auto=0&height=86"></iframe>
```

## 🔧 自定义设置

### 添加更多歌曲
1. 在 `content/Music/index.md` 中复制现有的音乐项目HTML结构
2. 更新歌曲信息
3. 在 `themes/hugo-goa/static/js/custom.js` 中的 `musicData` 对象添加新条目

### 修改样式
编辑 `themes/hugo-goa/static/css/custom.css` 中的音乐播放器相关样式

### 更改颜色主题
在 CSS 文件中修改以下变量：
- 主色调：`#667eea`
- 网易云红色：`#d33a31`
- 背景色：`#f8f9fa`

## 🚀 部署和测试

### 本地测试
```bash
cd /Users/<USER>/slywebsite
hugo server -D
```

访问 `http://localhost:1313/music/` 查看效果

### 部署到GitHub Pages
```bash
git add .
git commit -m "Add music player functionality"
git push origin main
```

## ⚠️ 注意事项

### 版权问题
- 只展示您拥有版权的原创作品
- 确保有权在网站上分享这些内容

### 音频文件大小
- 建议单个文件小于10MB
- 考虑使用压缩格式以提高加载速度

### 网易云音乐限制
- 外链播放器可能受到网易云音乐政策影响
- 建议同时提供直接链接到网易云音乐的选项

### 移动端优化
- 已包含响应式设计
- 在移动设备上测试播放功能

## 🛠️ 故障排除

### 音频无法播放
1. 检查音频文件路径是否正确
2. 确认浏览器支持音频格式
3. 检查文件权限

### 网易云播放器无法加载
1. 检查歌单/歌曲是否公开
2. 确认ID是否正确
3. 尝试不同的浏览器

### 样式显示异常
1. 清除浏览器缓存
2. 检查CSS文件是否正确加载
3. 验证HTML结构

## 📞 技术支持

如果遇到问题，可以：
1. 检查浏览器控制台的错误信息
2. 参考Hugo官方文档
3. 查看网易云音乐开发者文档

---

祝您使用愉快！🎵
