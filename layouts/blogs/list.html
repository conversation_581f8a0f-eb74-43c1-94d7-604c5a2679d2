{{ partial "header.html" . }}

<section id="blog-categories-pane" class="blog-categories">
  <div class="blog-header">
    <h1>{{ .Title }}</h1>
    <p class="blog-description">{{ .Description }}</p>
  </div>

  <!-- 分类统计 -->
  <div class="category-stats">
    {{ $blogPages := where .Site.RegularPages "Section" "Blogs" }}
    {{ $categories := slice }}
    {{ range $blogPages }}
      {{ range .Params.categories }}
        {{ $categories = $categories | append . }}
      {{ end }}
    {{ end }}
    {{ $uniqueCategories := $categories | uniq }}

    <div class="stats-grid">
      <div class="stat-item">
        <span class="stat-number">{{ len $blogPages }}</span>
        <span class="stat-label">篇文章</span>
      </div>
      <div class="stat-item">
        <span class="stat-number">{{ len $uniqueCategories }}</span>
        <span class="stat-label">个分类</span>
      </div>
    </div>
  </div>

  <!-- 分类展示 -->
  <div class="categories-container">
    {{ $blogPages := where .Site.RegularPages "Section" "Blogs" }}
    {{ $categoriesMap := dict }}
    
    <!-- 收集所有分类和对应的文章 -->
    {{ range $blogPages }}
      {{ range .Params.categories }}
        {{ $category := . }}
        {{ $articles := index $categoriesMap $category }}
        {{ if not $articles }}
          {{ $articles = slice }}
        {{ end }}
        {{ $articles = $articles | append $ }}
        {{ $categoriesMap = merge $categoriesMap (dict $category $articles) }}
      {{ end }}
    {{ end }}

    <!-- 显示每个分类 -->
    {{ range $category, $articles := $categoriesMap }}
    <div class="category-section">
      <div class="category-header">
        <h2 class="category-title">
          <i class="fas fa-folder-open"></i>
          {{ $category }}
          <span class="article-count">({{ len $articles }})</span>
        </h2>
      </div>
      
      <div class="articles-grid">
        {{ range $articles.ByDate.Reverse }}
        <article class="article-card">
          <div class="article-content">
            <h3 class="article-title">
              <a href="{{ .RelPermalink }}">{{ .Title }}</a>
            </h3>
            <p class="article-description">{{ .Description }}</p>
            <div class="article-meta">
              <span class="article-date">
                <i class="fas fa-calendar"></i>
                {{ .Date.Format "2006-01-02" }}
              </span>
              <span class="article-reading-time">
                <i class="fas fa-clock"></i>
                {{ math.Round (div (countwords .Content) 220.0) }} 分钟阅读
              </span>
              {{ if .Params.csdn_url }}
              <span class="article-csdn">
                <i class="fas fa-external-link-alt"></i>
                CSDN同步
              </span>
              {{ end }}
            </div>
            <div class="article-tags">
              {{ range .Params.categories }}
              <span class="tag">{{ . }}</span>
              {{ end }}
            </div>
          </div>
          <div class="article-actions">
            <a href="{{ .RelPermalink }}" class="read-more-btn">
              <span>阅读全文</span>
              <i class="fas fa-arrow-right"></i>
            </a>
          </div>
        </article>
        {{ end }}
      </div>
    </div>
    {{ end }}
  </div>
</section>

{{ partial "menu.html" . }}
{{ partial "footer.html" . }}
