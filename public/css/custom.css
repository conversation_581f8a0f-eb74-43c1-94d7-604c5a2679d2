/* Add custom CSS here. */

/* 音乐播放器样式 */
.music-player-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.playlist-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.playlist-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.playlist-description {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

.music-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.music-item {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.music-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: #667eea;
}

.music-cover {
  position: relative;
  width: 80px;
  height: 80px;
  margin-right: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.music-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.music-cover:hover img {
  transform: scale(1.05);
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.music-cover:hover .play-overlay {
  opacity: 1;
}

.play-btn {
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.play-btn:hover {
  transform: scale(1.1);
}

.music-info {
  flex: 1;
  min-width: 0;
}

.song-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.artist-name {
  margin: 0 0 0.8rem 0;
  color: #667eea;
  font-weight: 500;
  font-size: 0.95rem;
}

.song-description {
  margin: 0 0 1rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

.song-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: #999;
}

.duration, .release-date {
  display: flex;
  align-items: center;
}

.duration::before {
  content: "⏱️";
  margin-right: 0.3rem;
}

.release-date::before {
  content: "📅";
  margin-right: 0.3rem;
}

.music-actions {
  margin-left: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  min-width: 280px;
}

.netease-player-inline {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #f5f5f5;
}

.netease-player-inline iframe {
  width: 100%;
  min-height: 86px;
  border: none;
  display: block;
}

.netease-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  background: #d33a31;
  color: white !important;
  text-decoration: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: center;
}

.netease-link:hover {
  background: #b8302a;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(211, 58, 49, 0.3);
}

/* 网易云音乐播放器区域 */
.netease-player-section {
  margin: 3rem 0;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.netease-player-section h2 {
  margin-bottom: 1rem;
  color: #333;
  text-align: center;
}

.netease-player-section p {
  text-align: center;
  color: #666;
  margin-bottom: 1.5rem;
}

.netease-player-section iframe {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.featured-song {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.featured-song h3 {
  margin: 0 0 1rem 0;
  color: #333;
  text-align: center;
  font-size: 1.2rem;
}

.featured-song iframe {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* Shortcode 样式 */
.netease-player-wrapper {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #f5f5f5;
}

.netease-player-wrapper iframe {
  width: 100%;
  border: none;
  display: block;
}

.netease-player-error {
  padding: 1rem;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  color: #856404;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .music-item {
    flex-direction: column;
    text-align: center;
    padding: 1.2rem;
  }

  .music-cover {
    margin-right: 0;
    margin-bottom: 1rem;
    width: 100px;
    height: 100px;
  }

  .music-actions {
    margin-left: 0;
    margin-top: 1rem;
    min-width: auto;
    width: 100%;
  }

  .netease-player-inline {
    width: 100%;
  }

  .playlist-header h2 {
    font-size: 1.5rem;
  }

  .song-meta {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .music-actions {
    min-width: auto;
  }

  .netease-player-inline iframe {
    min-height: 66px;
  }
}
