<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>LLM on Sun longyu</title>
    <link>http://localhost:1313/categories/llm/</link>
    <description>Recent content in LLM on Sun longyu</description>
    <generator>Hugo</generator>
    <language>zh-cn</language>
    <lastBuildDate>Sat, 01 Feb 2025 04:55:54 +0530</lastBuildDate>
    <atom:link href="http://localhost:1313/categories/llm/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>LLM Pre-Training</title>
      <link>http://localhost:1313/blogs/llm-pre-training/</link>
      <pubDate>Sat, 01 Feb 2025 04:55:54 +0530</pubDate>
      <guid>http://localhost:1313/blogs/llm-pre-training/</guid>
      <description>&lt;h1 id=&#34;预训练---研发大语言模型的第一个训练阶段&#34;&gt;预训练 - 研发大语言模型的第一个训练阶段&lt;/h1&gt;&#xA;&lt;p&gt;预训练是研发大语言模型的第一个训练阶段，通过在大规模语料上进行预训练，大语言模型可以获得通用的语言理解与生成能力，掌握较为广泛的世界知识，具备解决众多下游任务的性能潜力。&lt;/p&gt;&#xA;&lt;h2 id=&#34;一数据预处理&#34;&gt;一、数据预处理&lt;/h2&gt;&#xA;&lt;h3 id=&#34;一数据的收集&#34;&gt;（一）数据的收集&lt;/h3&gt;&#xA;&lt;ol&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;通用文本数据（“主食”）&lt;/strong&gt;&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;来源&lt;/strong&gt; ：网页（C4 、RefinedWeb、CC-Stories 等）；书籍（Books3 、Bookcorpus2 等）。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;特点&lt;/strong&gt; ：量大；多样；需要清洗；注意搭配。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;专用文本数据（“特色”）&lt;/strong&gt;&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;多语言文本&lt;/strong&gt; ：加入大量非英语的文本数据，加强多语言任务的同时，还能促进不同语言的知识迁移，提升模型泛化能力（BLOOM 和 PaLM 模型使用了百种语言进行训练）。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;科学文本&lt;/strong&gt; ：加入大量科学文献、论文（比如 ArXiv 数据集）、教科书等，提升模型在专业领域的问答、推理和信息抽取能力（注意公式等符号需要采用特定的分词和预处理技术）。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;代码&lt;/strong&gt; ：加入海量来自 Stack Exchange、GitHub 等的代码数据，提升编程能力。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;/ol&gt;&#xA;&lt;h3 id=&#34;二数据预处理&#34;&gt;（二）数据预处理&lt;/h3&gt;&#xA;&lt;ol&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;质量过滤 - 去除低质量&lt;/strong&gt;&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;基于启发式规则&lt;/strong&gt; ：以精心设计的规则（基于语种、统计指标、关键词）识别和剔除低质量文本。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;基于分类器&lt;/strong&gt; ：用人工标注的高质量和低质量数据训练模型来判断文本质量，实现方法包括轻量级模型（如 FastText）、可微调的预训练语言模型（如 BERT、BART 或者 LLaMA 等）以及闭源大语言模型 API（如 GPT - 4）。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;敏感内容过滤 - 去除敏感&lt;/strong&gt;&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;有毒内容&lt;/strong&gt; ：采用基于分类器的过滤方法（Jigsaw 评论数据集可用于训练毒性分类器），通过设置合理的分类阈值，识别并过滤掉有毒内容。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;隐私内容&lt;/strong&gt; ：使用启发式方法（关键字识别）检测和删除私人信息。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;去重&lt;/strong&gt;&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;基于计算粒度&lt;/strong&gt; ：在句子级别、文档级别和数据集级别等多种粒度上进行去重，采用多阶段、多粒度的方式实现高效去重。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;基于匹配算法&lt;/strong&gt; ：文档层面使用开销较小的近似匹配（局部敏感哈希：minhash），句子层面使用精确匹配算法（后缀数组匹配最小长度的完全相同子串）。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;数据词元化（Tokenization）&lt;/strong&gt;&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;BPE&lt;/strong&gt; ：统计文本里最常相邻出现的组合，不断合并，直到达到预设的词库大小；字节级 BPE 可表示任何字符。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;WordPiece&lt;/strong&gt; ：选择能让整个文本可能性提升最大的词元对合并，合并前会训练语言模型对词元对进行评分。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;Unigram&lt;/strong&gt; ：从初始集合开始，迭代删除词元，采用期望最大化 EM 算法，逐步缩减零件库直到目标大小。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;分词器 Tokenizer&lt;/strong&gt; ：对于混合多领域多种格式的语料，制定具备无损重构、高压缩率、高适应性的分词器。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;流行的分词库 SentencePiece&lt;/strong&gt; ：Google 开源的库，支持 BPE 和 Unigram，很多大模型用它定制分词器。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;/ol&gt;&#xA;&lt;h3 id=&#34;三数据调度-data-scheduling&#34;&gt;（三）数据调度 Data Scheduling&lt;/h3&gt;&#xA;&lt;ol&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;数据混合配比&lt;/strong&gt;&lt;/p&gt;</description>
    </item>
    <item>
      <title>LLM入门</title>
      <link>http://localhost:1313/blogs/llm%E5%85%A5%E9%97%A8/</link>
      <pubDate>Tue, 14 Jan 2025 07:07:07 +0100</pubDate>
      <guid>http://localhost:1313/blogs/llm%E5%85%A5%E9%97%A8/</guid>
      <description>&lt;h1 id=&#34;一大模型概述&#34;&gt;一、大模型概述&lt;/h1&gt;&#xA;&lt;h2 id=&#34;1大模型概念&#34;&gt;1、大模型概念&lt;/h2&gt;&#xA;&lt;p&gt;LLM 是指用有大量参数的大型预训练语言模型，在解决各种自然语言处理任务方面表现出强大的能力，甚至可以展现出一些小规模语言模型所不具备的特殊能力。&lt;/p&gt;&#xA;&lt;h2 id=&#34;2语言模型-language-model&#34;&gt;2、语言模型 language model&lt;/h2&gt;&#xA;&lt;p&gt;语言建模旨在对词序列的生成概率进行建模，以预测未来 tokens 的概率，语言模型的发展：&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;统计语言模型 SLM&lt;/strong&gt; ：统计语言模型使用马尔可夫假设（Markov Assumption）来建立语言序列的预测模型，通常是根据词序列中若干个连续的上下文单词来预测下一个词的出现概率，经典的例子是 n - gram 模型，在此模型中一个词出现的概率只依赖于前面的 n - 1 个词，比如一个 3gram 模型只考虑前两个词对第三个词出现概率的影响。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;神经语言模型 NLM&lt;/strong&gt; ：使用神经网络来预测词序列的概率分布，如 RNN 包括 LSTM 和 GRU 等变体，这样 NLM 就可以考虑更长的上下文或整个句子的信息，而传统的统计语言模型使用固定窗口大小的词来预测；在该模型中引入分布式词表示，每个单词被编码为实数值向量，即词嵌入（word embeddings）用来捕捉词与词之间的语法关系。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;预训练语言模型 PLM&lt;/strong&gt; ：PLM 开始在规模无标签语料库上进行预训练任务，学习语言规律知识，并且针对特定任务进行微调（fine - tuning）来适应不同应用场景；而对于大规模的长文本，谷歌提出了 transformer，通过自注意力机制（self - attention）和高度并行化能力，可以在处理序列数据时捕捉全局依赖关系，极大提高序列处理任务的效率。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;大语言模型 LLM&lt;/strong&gt; ：当一些研究工作尝试训练更大的预训练语言模型（例如 175B 参数的 GPT - 3 和 540B 参数的 PaLM）来探索扩展语言模型所带来的性能极限。这些大规模的预训练语言模型在解决复杂任务时表现出了与小型预训练语言模型（如 330M 参数的 BERT 和 1.5B 参数的 GPT2）不同的行为，这种大模型具有但小模型不具备的能力通常被称为 “涌现能力”（Emergent Abilities），这些大型的预训练模型就是 LLM。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h2 id=&#34;3大模型特点&#34;&gt;3、大模型特点&lt;/h2&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;参数数量庞大，数据需求巨大&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;计算资源密集&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;泛化能力强&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;迁移学习效果佳&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h2 id=&#34;4小模型-vs-大模型&#34;&gt;4、小模型 vs 大模型&lt;/h2&gt;&#xA;&lt;h2 id=&#34;5大模型企业应用&#34;&gt;5、大模型企业应用&lt;/h2&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;通用大模型&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;行业大模型&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;产业大模型&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h1 id=&#34;二大模型基础&#34;&gt;二、大模型基础&lt;/h1&gt;&#xA;&lt;h2 id=&#34;1大模型构建过程&#34;&gt;1、大模型构建过程&lt;/h2&gt;&#xA;&lt;h3 id=&#34;1大规模预训练large---scale-pre---training&#34;&gt;（1）大规模预训练（Large - Scale Pre - training）&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;目标&lt;/strong&gt; ：为模型参数找到好的 “初值点”，使其编码世界知识，具备通用的语言理解和生成能力。可以看作是世界知识的压缩。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;方法&lt;/strong&gt; ：使用海量（当前普遍 2 - 3T tokens 规模，并有扩大趋势）的无标注文本数据，通过自监督学习任务（当前主流是 “预测下一个词”）训练解码器架构（Decoder Architecture）模型。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;关键要素&lt;/strong&gt; ：&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;数据&lt;/strong&gt; ：高质量、多源化数据的收集与严格清洗至关重要，直接影响模型能力。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;算力&lt;/strong&gt; ：需求极高（百亿模型需数百卡，千亿模型需数千甚至万卡集群），训练时间长。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;技术与人才&lt;/strong&gt; ：涉及大量经验性技术（数据配比、学习率调整、异常检测等），高度依赖研发人员的经验和能力。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;2指令微调与人类对齐instruction-fine---tuning--human-alignment&#34;&gt;（2）指令微调与人类对齐（Instruction Fine - tuning &amp;amp; Human Alignment）&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;动机&lt;/strong&gt; ：预训练模型虽有知识，但不擅长直接按指令解决任务。需要进一步训练以适应人类的使用方式和价值观。&lt;/p&gt;</description>
    </item>
  </channel>
</rss>
