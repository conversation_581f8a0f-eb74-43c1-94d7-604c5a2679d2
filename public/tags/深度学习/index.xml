<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>深度学习 on Sun longyu</title>
    <link>http://localhost:1313/tags/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0/</link>
    <description>Recent content in 深度学习 on Sun longyu</description>
    <generator>Hugo</generator>
    <language>zh-cn</language>
    <lastBuildDate>Sun, 14 Jan 2024 07:07:07 +0100</lastBuildDate>
    <atom:link href="http://localhost:1313/tags/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>LLM入门</title>
      <link>http://localhost:1313/blogs/first/</link>
      <pubDate>Sun, 14 Jan 2024 07:07:07 +0100</pubDate>
      <guid>http://localhost:1313/blogs/first/</guid>
      <description>&lt;h2 id=&#34;introduction&#34;&gt;Introduction&lt;/h2&gt;&#xA;&lt;p&gt;Welcome to my first post using Hugo! In this post, I&amp;rsquo;m going to showcase some basic Markdown formatting and discuss why I chose Hugo for my blogging platform.&lt;/p&gt;&#xA;&lt;p&gt;This is &lt;strong&gt;bold&lt;/strong&gt; text, and this is &lt;em&gt;emphasized&lt;/em&gt; text.&lt;/p&gt;&#xA;&lt;h2 id=&#34;why-hugo&#34;&gt;Why Hugo?&lt;/h2&gt;&#xA;&lt;p&gt;&lt;a href=&#34;https://gohugo.io&#34;&gt;Hugo&lt;/a&gt; is a fast and flexible static site generator, perfect for blogs, portfolios, and even company websites. Here&amp;rsquo;s why I love it:&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;Speed&lt;/strong&gt;: Hugo generates pages at an incredibly fast speed.&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;Flexibility&lt;/strong&gt;: You can create any type of website with Hugo.&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;Community&lt;/strong&gt;: The Hugo community is supportive and continuously growing.&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h2 id=&#34;markdown-basics&#34;&gt;Markdown Basics&lt;/h2&gt;&#xA;&lt;p&gt;Markdown is a lightweight markup language that you can use to add formatting elements to plaintext text documents. Here are some basics:&lt;/p&gt;</description>
    </item>
  </channel>
</rss>
