<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>原创 on Sun longyu</title>
    <link>http://localhost:1313/tags/%E5%8E%9F%E5%88%9B/</link>
    <description>Recent content in 原创 on Sun longyu</description>
    <generator>Hugo</generator>
    <language>zh-cn</language>
    <lastBuildDate>Sun, 15 Dec 2024 15:00:00 +0800</lastBuildDate>
    <atom:link href="http://localhost:1313/tags/%E5%8E%9F%E5%88%9B/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>我的音乐作品</title>
      <link>http://localhost:1313/music/</link>
      <pubDate>Sun, 15 Dec 2024 15:00:00 +0800</pubDate>
      <guid>http://localhost:1313/music/</guid>
      <description>&lt;h1 id=&#34;-我的音乐世界&#34;&gt;🎵 我的音乐世界&lt;/h1&gt;&#xA;&lt;p&gt;欢迎来到我的音乐空间！这里收录了我在网易云音乐平台上发布的原创作品。每一首歌都承载着不同的情感和故事，希望能与你产生共鸣。&lt;/p&gt;&#xA;&lt;hr&gt;&#xA;&lt;div class=&#34;music-player-container&#34;&gt;&#xA;  &lt;div class=&#34;playlist-header&#34;&gt;&#xA;    &lt;h2&gt;🎼 我的原创作品集&lt;/h2&gt;&#xA;    &lt;p class=&#34;playlist-description&#34;&gt;点击播放按钮即可在线试听&lt;/p&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-list&#34;&gt;&#xA;    &lt;!-- 音乐作品1 --&gt;&#xA;    &lt;div class=&#34;music-item&#34; data-song-id=&#34;1&#34;&gt;&#xA;      &lt;div class=&#34;music-cover&#34;&gt;&#xA;        &lt;img src=&#34;http://localhost:1313/images/music/我想.png&#34; alt=&#34;歌曲封面&#34; onerror=&#34;this.src=&#39;http://localhost:1313/images/music/default-cover.svg&#39;&#34;&gt;&#xA;        &lt;div class=&#34;play-overlay&#34;&gt;&#xA;          &lt;i class=&#34;fas fa-play play-btn&#34;&gt;&lt;/i&gt;&#xA;        &lt;/div&gt;&#xA;      &lt;/div&gt;&#xA;      &lt;div class=&#34;music-info&#34;&gt;&#xA;        &lt;h3 class=&#34;song-title&#34;&gt;我想&lt;/h3&gt;&#xA;        &lt;p class=&#34;artist-name&#34;&gt;SLY司赖&lt;/p&gt;&#xA;        &lt;p class=&#34;song-description&#34;&gt;一首关于梦想和坚持的歌曲，献给所有在黑暗中寻找光明的人。&lt;/p&gt;&#xA;        &lt;div class=&#34;song-meta&#34;&gt;&#xA;          &lt;span class=&#34;duration&#34;&gt;02:47&lt;/span&gt;&#xA;          &lt;span class=&#34;release-date&#34;&gt;2024-07-11&lt;/span&gt;&#xA;        &lt;/div&gt;&#xA;      &lt;/div&gt;&#xA;      &lt;div class=&#34;music-actions&#34;&gt;&#xA;        &lt;a href=&#34;https://music.163.com/song?id=2606387759&amp;uct2=U2FsdGVkX18+sj7PYQ7JjdFLjyQ/xb6+8XIe9N1RJnE=&#34; target=&#34;_blank&#34; class=&#34;netease-link&#34;&gt;&#xA;          &lt;i class=&#34;fas fa-external-link-alt&#34;&gt;&lt;/i&gt; 网易云音乐&#xA;        &lt;/a&gt;&#xA;      &lt;/div&gt;&#xA;    &lt;/div&gt;&#xA;&lt;pre&gt;&lt;code&gt;&amp;lt;!-- 音乐作品2 --&amp;gt;&#xA;&amp;lt;div class=&amp;quot;music-item&amp;quot; data-song-id=&amp;quot;2&amp;quot;&amp;gt;&#xA;  &amp;lt;div class=&amp;quot;music-cover&amp;quot;&amp;gt;&#xA;    &amp;lt;img src=&amp;quot;/images/music/default-cover.svg&amp;quot; alt=&amp;quot;歌曲封面&amp;quot; onerror=&amp;quot;this.src=&#39;http://localhost:1313/images/music/default-cover.svg&#39;&amp;quot;&amp;gt;&#xA;    &amp;lt;div class=&amp;quot;play-overlay&amp;quot;&amp;gt;&#xA;      &amp;lt;i class=&amp;quot;fas fa-play play-btn&amp;quot;&amp;gt;&amp;lt;/i&amp;gt;&#xA;    &amp;lt;/div&amp;gt;&#xA;  &amp;lt;/div&amp;gt;&#xA;  &amp;lt;div class=&amp;quot;music-info&amp;quot;&amp;gt;&#xA;    &amp;lt;h3 class=&amp;quot;song-title&amp;quot;&amp;gt;时光倒流&amp;lt;/h3&amp;gt;&#xA;    &amp;lt;p class=&amp;quot;artist-name&amp;quot;&amp;gt;SLY&amp;lt;/p&amp;gt;&#xA;    &amp;lt;p class=&amp;quot;song-description&amp;quot;&amp;gt;回忆青春岁月的温柔旋律，每个人心中都有一段美好的时光。&amp;lt;/p&amp;gt;&#xA;    &amp;lt;div class=&amp;quot;song-meta&amp;quot;&amp;gt;&#xA;      &amp;lt;span class=&amp;quot;duration&amp;quot;&amp;gt;04:12&amp;lt;/span&amp;gt;&#xA;      &amp;lt;span class=&amp;quot;release-date&amp;quot;&amp;gt;2024-02-20&amp;lt;/span&amp;gt;&#xA;    &amp;lt;/div&amp;gt;&#xA;  &amp;lt;/div&amp;gt;&#xA;  &amp;lt;div class=&amp;quot;music-actions&amp;quot;&amp;gt;&#xA;    &amp;lt;a href=&amp;quot;https://music.163.com/#/song?id=YOUR_SONG_ID_2&amp;quot; target=&amp;quot;_blank&amp;quot; class=&amp;quot;netease-link&amp;quot;&amp;gt;&#xA;      &amp;lt;i class=&amp;quot;fas fa-external-link-alt&amp;quot;&amp;gt;&amp;lt;/i&amp;gt; 网易云音乐&#xA;    &amp;lt;/a&amp;gt;&#xA;  &amp;lt;/div&amp;gt;&#xA;&amp;lt;/div&amp;gt;&#xA;&#xA;&amp;lt;!-- 音乐作品3 --&amp;gt;&#xA;&amp;lt;div class=&amp;quot;music-item&amp;quot; data-song-id=&amp;quot;3&amp;quot;&amp;gt;&#xA;  &amp;lt;div class=&amp;quot;music-cover&amp;quot;&amp;gt;&#xA;    &amp;lt;img src=&amp;quot;/images/music/default-cover.svg&amp;quot; alt=&amp;quot;歌曲封面&amp;quot; onerror=&amp;quot;this.src=&#39;http://localhost:1313/images/music/default-cover.svg&#39;&amp;quot;&amp;gt;&#xA;    &amp;lt;div class=&amp;quot;play-overlay&amp;quot;&amp;gt;&#xA;      &amp;lt;i class=&amp;quot;fas fa-play play-btn&amp;quot;&amp;gt;&amp;lt;/i&amp;gt;&#xA;    &amp;lt;/div&amp;gt;&#xA;  &amp;lt;/div&amp;gt;&#xA;  &amp;lt;div class=&amp;quot;music-info&amp;quot;&amp;gt;&#xA;    &amp;lt;h3 class=&amp;quot;song-title&amp;quot;&amp;gt;城市漫步&amp;lt;/h3&amp;gt;&#xA;    &amp;lt;p class=&amp;quot;artist-name&amp;quot;&amp;gt;SLY&amp;lt;/p&amp;gt;&#xA;    &amp;lt;p class=&amp;quot;song-description&amp;quot;&amp;gt;都市生活的节奏感，描绘现代人在城市中的生活状态。&amp;lt;/p&amp;gt;&#xA;    &amp;lt;div class=&amp;quot;song-meta&amp;quot;&amp;gt;&#xA;      &amp;lt;span class=&amp;quot;duration&amp;quot;&amp;gt;03:28&amp;lt;/span&amp;gt;&#xA;      &amp;lt;span class=&amp;quot;release-date&amp;quot;&amp;gt;2024-03-10&amp;lt;/span&amp;gt;&#xA;    &amp;lt;/div&amp;gt;&#xA;  &amp;lt;/div&amp;gt;&#xA;  &amp;lt;div class=&amp;quot;music-actions&amp;quot;&amp;gt;&#xA;    &amp;lt;a href=&amp;quot;https://music.163.com/#/song?id=YOUR_SONG_ID_3&amp;quot; target=&amp;quot;_blank&amp;quot; class=&amp;quot;netease-link&amp;quot;&amp;gt;&#xA;      &amp;lt;i class=&amp;quot;fas fa-external-link-alt&amp;quot;&amp;gt;&amp;lt;/i&amp;gt; 网易云音乐&#xA;    &amp;lt;/a&amp;gt;&#xA;  &amp;lt;/div&amp;gt;&#xA;&amp;lt;/div&amp;gt;&#xA;&lt;/code&gt;&lt;/pre&gt;&#xA;  &lt;/div&gt;&#xA;&lt;/div&gt;&#xA;&lt;!-- 网易云音乐播放器嵌入 --&gt;&#xA;&lt;div class=&#34;netease-player-section&#34;&gt;&#xA;  &lt;h2&gt;🎧 在线播放器&lt;/h2&gt;&#xA;  &lt;p&gt;以下是我的网易云音乐歌单，可以直接在线播放：&lt;/p&gt;</description>
    </item>
  </channel>
</rss>
