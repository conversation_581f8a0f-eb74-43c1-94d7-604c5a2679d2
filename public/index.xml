<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Sun longyu</title>
    <link>http://localhost:1313/</link>
    <description>Recent content on Sun longyu</description>
    <generator>Hugo</generator>
    <language>zh-cn</language>
    <lastBuildDate>Sat, 01 Feb 2025 04:55:54 +0530</lastBuildDate>
    <atom:link href="http://localhost:1313/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>LLM Pre-Training</title>
      <link>http://localhost:1313/blogs/llm-pre-training/</link>
      <pubDate>Sat, 01 Feb 2025 04:55:54 +0530</pubDate>
      <guid>http://localhost:1313/blogs/llm-pre-training/</guid>
      <description>&lt;h1 id=&#34;预训练---研发大语言模型的第一个训练阶段&#34;&gt;预训练 - 研发大语言模型的第一个训练阶段&lt;/h1&gt;&#xA;&lt;p&gt;预训练是研发大语言模型的第一个训练阶段，通过在大规模语料上进行预训练，大语言模型可以获得通用的语言理解与生成能力，掌握较为广泛的世界知识，具备解决众多下游任务的性能潜力。&lt;/p&gt;&#xA;&lt;h2 id=&#34;一数据预处理&#34;&gt;一、数据预处理&lt;/h2&gt;&#xA;&lt;h3 id=&#34;一数据的收集&#34;&gt;（一）数据的收集&lt;/h3&gt;&#xA;&lt;ol&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;通用文本数据（“主食”）&lt;/strong&gt;&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;来源&lt;/strong&gt; ：网页（C4 、RefinedWeb、CC-Stories 等）；书籍（Books3 、Bookcorpus2 等）。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;特点&lt;/strong&gt; ：量大；多样；需要清洗；注意搭配。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;专用文本数据（“特色”）&lt;/strong&gt;&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;多语言文本&lt;/strong&gt; ：加入大量非英语的文本数据，加强多语言任务的同时，还能促进不同语言的知识迁移，提升模型泛化能力（BLOOM 和 PaLM 模型使用了百种语言进行训练）。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;科学文本&lt;/strong&gt; ：加入大量科学文献、论文（比如 ArXiv 数据集）、教科书等，提升模型在专业领域的问答、推理和信息抽取能力（注意公式等符号需要采用特定的分词和预处理技术）。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;代码&lt;/strong&gt; ：加入海量来自 Stack Exchange、GitHub 等的代码数据，提升编程能力。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;/ol&gt;&#xA;&lt;h3 id=&#34;二数据预处理&#34;&gt;（二）数据预处理&lt;/h3&gt;&#xA;&lt;ol&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;质量过滤 - 去除低质量&lt;/strong&gt;&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;基于启发式规则&lt;/strong&gt; ：以精心设计的规则（基于语种、统计指标、关键词）识别和剔除低质量文本。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;基于分类器&lt;/strong&gt; ：用人工标注的高质量和低质量数据训练模型来判断文本质量，实现方法包括轻量级模型（如 FastText）、可微调的预训练语言模型（如 BERT、BART 或者 LLaMA 等）以及闭源大语言模型 API（如 GPT - 4）。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;敏感内容过滤 - 去除敏感&lt;/strong&gt;&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;有毒内容&lt;/strong&gt; ：采用基于分类器的过滤方法（Jigsaw 评论数据集可用于训练毒性分类器），通过设置合理的分类阈值，识别并过滤掉有毒内容。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;隐私内容&lt;/strong&gt; ：使用启发式方法（关键字识别）检测和删除私人信息。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;去重&lt;/strong&gt;&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;基于计算粒度&lt;/strong&gt; ：在句子级别、文档级别和数据集级别等多种粒度上进行去重，采用多阶段、多粒度的方式实现高效去重。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;基于匹配算法&lt;/strong&gt; ：文档层面使用开销较小的近似匹配（局部敏感哈希：minhash），句子层面使用精确匹配算法（后缀数组匹配最小长度的完全相同子串）。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;数据词元化（Tokenization）&lt;/strong&gt;&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;BPE&lt;/strong&gt; ：统计文本里最常相邻出现的组合，不断合并，直到达到预设的词库大小；字节级 BPE 可表示任何字符。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;WordPiece&lt;/strong&gt; ：选择能让整个文本可能性提升最大的词元对合并，合并前会训练语言模型对词元对进行评分。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;Unigram&lt;/strong&gt; ：从初始集合开始，迭代删除词元，采用期望最大化 EM 算法，逐步缩减零件库直到目标大小。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;分词器 Tokenizer&lt;/strong&gt; ：对于混合多领域多种格式的语料，制定具备无损重构、高压缩率、高适应性的分词器。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;流行的分词库 SentencePiece&lt;/strong&gt; ：Google 开源的库，支持 BPE 和 Unigram，很多大模型用它定制分词器。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;/ol&gt;&#xA;&lt;h3 id=&#34;三数据调度-data-scheduling&#34;&gt;（三）数据调度 Data Scheduling&lt;/h3&gt;&#xA;&lt;ol&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;数据混合配比&lt;/strong&gt;&lt;/p&gt;</description>
    </item>
    <item>
      <title>LLM入门</title>
      <link>http://localhost:1313/blogs/llm%E5%85%A5%E9%97%A8/</link>
      <pubDate>Tue, 14 Jan 2025 07:07:07 +0100</pubDate>
      <guid>http://localhost:1313/blogs/llm%E5%85%A5%E9%97%A8/</guid>
      <description>&lt;h1 id=&#34;一大模型概述&#34;&gt;一、大模型概述&lt;/h1&gt;&#xA;&lt;h2 id=&#34;1大模型概念&#34;&gt;1、大模型概念&lt;/h2&gt;&#xA;&lt;p&gt;LLM 是指用有大量参数的大型预训练语言模型，在解决各种自然语言处理任务方面表现出强大的能力，甚至可以展现出一些小规模语言模型所不具备的特殊能力。&lt;/p&gt;&#xA;&lt;h2 id=&#34;2语言模型-language-model&#34;&gt;2、语言模型 language model&lt;/h2&gt;&#xA;&lt;p&gt;语言建模旨在对词序列的生成概率进行建模，以预测未来 tokens 的概率，语言模型的发展：&lt;/p&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;统计语言模型 SLM&lt;/strong&gt; ：统计语言模型使用马尔可夫假设（Markov Assumption）来建立语言序列的预测模型，通常是根据词序列中若干个连续的上下文单词来预测下一个词的出现概率，经典的例子是 n - gram 模型，在此模型中一个词出现的概率只依赖于前面的 n - 1 个词，比如一个 3gram 模型只考虑前两个词对第三个词出现概率的影响。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;神经语言模型 NLM&lt;/strong&gt; ：使用神经网络来预测词序列的概率分布，如 RNN 包括 LSTM 和 GRU 等变体，这样 NLM 就可以考虑更长的上下文或整个句子的信息，而传统的统计语言模型使用固定窗口大小的词来预测；在该模型中引入分布式词表示，每个单词被编码为实数值向量，即词嵌入（word embeddings）用来捕捉词与词之间的语法关系。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;预训练语言模型 PLM&lt;/strong&gt; ：PLM 开始在规模无标签语料库上进行预训练任务，学习语言规律知识，并且针对特定任务进行微调（fine - tuning）来适应不同应用场景；而对于大规模的长文本，谷歌提出了 transformer，通过自注意力机制（self - attention）和高度并行化能力，可以在处理序列数据时捕捉全局依赖关系，极大提高序列处理任务的效率。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;大语言模型 LLM&lt;/strong&gt; ：当一些研究工作尝试训练更大的预训练语言模型（例如 175B 参数的 GPT - 3 和 540B 参数的 PaLM）来探索扩展语言模型所带来的性能极限。这些大规模的预训练语言模型在解决复杂任务时表现出了与小型预训练语言模型（如 330M 参数的 BERT 和 1.5B 参数的 GPT2）不同的行为，这种大模型具有但小模型不具备的能力通常被称为 “涌现能力”（Emergent Abilities），这些大型的预训练模型就是 LLM。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h2 id=&#34;3大模型特点&#34;&gt;3、大模型特点&lt;/h2&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;参数数量庞大，数据需求巨大&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;计算资源密集&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;泛化能力强&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;迁移学习效果佳&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h2 id=&#34;4小模型-vs-大模型&#34;&gt;4、小模型 vs 大模型&lt;/h2&gt;&#xA;&lt;h2 id=&#34;5大模型企业应用&#34;&gt;5、大模型企业应用&lt;/h2&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;通用大模型&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;行业大模型&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;产业大模型&lt;/strong&gt;&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h1 id=&#34;二大模型基础&#34;&gt;二、大模型基础&lt;/h1&gt;&#xA;&lt;h2 id=&#34;1大模型构建过程&#34;&gt;1、大模型构建过程&lt;/h2&gt;&#xA;&lt;h3 id=&#34;1大规模预训练large---scale-pre---training&#34;&gt;（1）大规模预训练（Large - Scale Pre - training）&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;目标&lt;/strong&gt; ：为模型参数找到好的 “初值点”，使其编码世界知识，具备通用的语言理解和生成能力。可以看作是世界知识的压缩。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;方法&lt;/strong&gt; ：使用海量（当前普遍 2 - 3T tokens 规模，并有扩大趋势）的无标注文本数据，通过自监督学习任务（当前主流是 “预测下一个词”）训练解码器架构（Decoder Architecture）模型。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;关键要素&lt;/strong&gt; ：&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;数据&lt;/strong&gt; ：高质量、多源化数据的收集与严格清洗至关重要，直接影响模型能力。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;算力&lt;/strong&gt; ：需求极高（百亿模型需数百卡，千亿模型需数千甚至万卡集群），训练时间长。&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;技术与人才&lt;/strong&gt; ：涉及大量经验性技术（数据配比、学习率调整、异常检测等），高度依赖研发人员的经验和能力。&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;2指令微调与人类对齐instruction-fine---tuning--human-alignment&#34;&gt;（2）指令微调与人类对齐（Instruction Fine - tuning &amp;amp; Human Alignment）&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&#xA;&lt;p&gt;&lt;strong&gt;动机&lt;/strong&gt; ：预训练模型虽有知识，但不擅长直接按指令解决任务。需要进一步训练以适应人类的使用方式和价值观。&lt;/p&gt;</description>
    </item>
    <item>
      <title>我的音乐作品</title>
      <link>http://localhost:1313/music/</link>
      <pubDate>Sun, 15 Dec 2024 15:00:00 +0800</pubDate>
      <guid>http://localhost:1313/music/</guid>
      <description>&lt;h1 id=&#34;-我的音乐世界&#34;&gt;🎵 我的音乐世界&lt;/h1&gt;&#xA;&lt;p&gt;欢迎来到我的音乐空间！这里收录了我在网易云音乐平台上发布的原创作品。每一首歌都承载着不同的情感和故事，希望能与你产生共鸣。&lt;/p&gt;&#xA;&lt;hr&gt;&#xA;&lt;div class=&#34;music-player-container&#34;&gt;&#xA;  &lt;div class=&#34;playlist-header&#34;&gt;&#xA;    &lt;h2&gt;🎼 我的原创作品集&lt;/h2&gt;&#xA;    &lt;p class=&#34;playlist-description&#34;&gt;点击播放按钮即可在线试听&lt;/p&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-list&#34;&gt;&#xA;&lt;div class=&#34;music-item&#34; data-song-id=&#34;1&#34;&gt;&#xA;  &lt;div class=&#34;music-cover&#34;&gt;&#xA;    &lt;img src=&#34;http://localhost:1313/images/music/我想.png&#34; alt=&#34;歌曲封面&#34; onerror=&#34;this.src=&#39;http://localhost:1313/images/music/default-cover.svg&#39;&#34;&gt;&#xA;    &lt;div class=&#34;play-overlay&#34;&gt;&#xA;      &lt;i class=&#34;fas fa-play play-btn&#34;&gt;&lt;/i&gt;&#xA;    &lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-info&#34;&gt;&#xA;    &lt;h3 class=&#34;song-title&#34;&gt;我想&lt;/h3&gt;&#xA;    &lt;p class=&#34;artist-name&#34;&gt;SLY司赖&lt;/p&gt;&#xA;    &lt;div class=&#34;song-meta&#34;&gt;&#xA;      &lt;span class=&#34;duration&#34;&gt;02:47&lt;/span&gt;&#xA;      &lt;span class=&#34;release-date&#34;&gt;2024-07-11&lt;/span&gt;&#xA;    &lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-actions&#34;&gt;&#xA;    &lt;div class=&#34;netease-player-inline&#34;&gt;&#xA;      &lt;iframe frameborder=&#34;no&#34; border=&#34;0&#34; marginwidth=&#34;0&#34; marginheight=&#34;0&#34; width=330 height=&#34;86&#34; src=&#34;//music.163.com/outchain/player?type=2&amp;id=2606387759&amp;auto=0&amp;height=66&#34;&gt;&lt;/iframe&gt;&#xA;    &lt;/div&gt;&#xA;    &lt;a href=&#34;https://music.163.com/song?id=2606387759&amp;uct2=U2FsdGVkX18+sj7PYQ7JjdFLjyQ/xb6+8XIe9N1RJnE=&#34; target=&#34;_blank&#34; class=&#34;netease-link-small&#34;&gt;&#xA;      &lt;i class=&#34;fas fa-external-link-alt&#34;&gt;&lt;/i&gt; 在网易云音乐中打开&#xA;    &lt;/a&gt;&#xA;  &lt;/div&gt;&#xA;&lt;/div&gt;&#xA;&lt;div class=&#34;music-item&#34; data-song-id=&#34;2&#34;&gt;&#xA;  &lt;div class=&#34;music-cover&#34;&gt;&#xA;    &lt;img src=&#34;http://localhost:1313/images/music/我也想在你想我的时候想着你.jpg&#34; alt=&#34;歌曲封面&#34; onerror=&#34;this.src=&#39;http://localhost:1313/images/music/default-cover.svg&#39;&#34;&gt;&#xA;    &lt;div class=&#34;play-overlay&#34;&gt;&#xA;      &lt;i class=&#34;fas fa-play play-btn&#34;&gt;&lt;/i&gt;&#xA;    &lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-info&#34;&gt;&#xA;    &lt;h3 class=&#34;song-title&#34;&gt;我也想在你想我的时候想着你&lt;/h3&gt;&#xA;    &lt;p class=&#34;artist-name&#34;&gt;SLY司赖&lt;/p&gt;&#xA;    &lt;div class=&#34;song-meta&#34;&gt;&#xA;      &lt;span class=&#34;duration&#34;&gt;02:54&lt;/span&gt;&#xA;      &lt;span class=&#34;release-date&#34;&gt;2024-02-24&lt;/span&gt;&#xA;    &lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-actions&#34;&gt;&#xA;    &lt;div class=&#34;netease-player-inline&#34;&gt;&#xA;      &lt;iframe frameborder=&#34;no&#34; border=&#34;0&#34; marginwidth=&#34;0&#34; marginheight=&#34;0&#34; width=&#34;100%&#34; height=&#34;86&#34; src=&#34;//music.163.com/outchain/player?type=2&amp;id=2127901953&amp;auto=0&amp;height=66&#34;&gt;&lt;/iframe&gt;&#xA;    &lt;/div&gt;&#xA;    &lt;a href=&#34;https://music.163.com/song?id=2127901953&#34; target=&#34;_blank&#34; class=&#34;netease-link-small&#34;&gt;&#xA;      &lt;i class=&#34;fas fa-external-link-alt&#34;&gt;&lt;/i&gt; 在网易云音乐中打开&#xA;    &lt;/a&gt;&#xA;  &lt;/div&gt;&#xA;&lt;/div&gt;&#xA;&lt;div class=&#34;music-item&#34; data-song-id=&#34;3&#34;&gt;&#xA;  &lt;div class=&#34;music-cover&#34;&gt;&#xA;    &lt;img src=&#34;http://localhost:1313/images/music/想过.jpg&#34; alt=&#34;歌曲封面&#34; onerror=&#34;this.src=&#39;http://localhost:1313/images/music/default-cover.svg&#39;&#34;&gt;&#xA;    &lt;div class=&#34;play-overlay&#34;&gt;&#xA;      &lt;i class=&#34;fas fa-play play-btn&#34;&gt;&lt;/i&gt;&#xA;    &lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-info&#34;&gt;&#xA;    &lt;h3 class=&#34;song-title&#34;&gt;想·过&lt;/h3&gt;&#xA;    &lt;p class=&#34;artist-name&#34;&gt;SLY司赖&lt;/p&gt;&#xA;    &lt;div class=&#34;song-meta&#34;&gt;&#xA;      &lt;span class=&#34;duration&#34;&gt;03:26&lt;/span&gt;&#xA;      &lt;span class=&#34;release-date&#34;&gt;2023-07-11&lt;/span&gt;&#xA;    &lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-actions&#34;&gt;&#xA;    &lt;div class=&#34;netease-player-inline&#34;&gt;&#xA;      &lt;iframe frameborder=&#34;no&#34; border=&#34;0&#34; marginwidth=&#34;0&#34; marginheight=&#34;0&#34; width=330 height=86 src=&#34;//music.163.com/outchain/player?type=2&amp;id=2063028431&amp;auto=0&amp;height=66&#34;&gt;&lt;/iframe&gt;&#xA;    &lt;/div&gt;&#xA;    &lt;a href=&#34;https://music.163.com/song?id=2063028431&amp;uct2=U2FsdGVkX1+UNuLnE/0v7E8tYV0kPpwwZFFHGFneioU=&#xA;      &lt;i class=&#34;fas fa-external-link-alt&#34;&gt;&lt;/i&gt; 在网易云音乐中打开&#xA;    &lt;/a&gt;&#xA;  &lt;/div&gt;&#xA;&lt;/div&gt;&#xA;&lt;div class=&#34;music-item&#34; data-song-id=&#34;4&#34;&gt;&#xA;  &lt;div class=&#34;music-cover&#34;&gt;&#xA;    &lt;img src=&#34;http://localhost:1313/images/music/英雄.jpg&#34; alt=&#34;歌曲封面&#34; onerror=&#34;this.src=&#39;http://localhost:1313/images/music/default-cover.svg&#39;&#34;&gt;&#xA;    &lt;div class=&#34;play-overlay&#34;&gt;&#xA;      &lt;i class=&#34;fas fa-play play-btn&#34;&gt;&lt;/i&gt;&#xA;    &lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-info&#34;&gt;&#xA;    &lt;h3 class=&#34;song-title&#34;&gt;英雄&lt;/h3&gt;&#xA;    &lt;p class=&#34;artist-name&#34;&gt;SLY司赖&lt;/p&gt;&#xA;    &lt;div class=&#34;song-meta&#34;&gt;&#xA;      &lt;span class=&#34;duration&#34;&gt;02:15&lt;/span&gt;&#xA;      &lt;span class=&#34;release-date&#34;&gt;2023-07-05&lt;/span&gt;&#xA;    &lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-actions&#34;&gt;&#xA;    &lt;div class=&#34;netease-player-inline&#34;&gt;&#xA;      &lt;iframe frameborder=&#34;no&#34; border=&#34;0&#34; marginwidth=&#34;0&#34; marginheight=&#34;0&#34; width=330 height=86 src=&#34;//music.163.com/outchain/player?type=2&amp;id=2060963543&amp;auto=0&amp;height=66&#34;&gt;&lt;/iframe&gt;&#xA;    &lt;/div&gt;&#xA;    &lt;a href=&#34;https://music.163.com/song?id=2060963543&amp;uct2=U2FsdGVkX19A3vVO/tUij/lNKYYGGC95lbW0aUaggKw=&#xA;      &lt;i class=&#34;fas fa-external-link-alt&#34;&gt;&lt;/i&gt; 在网易云音乐中打开&#xA;    &lt;/a&gt;&#xA;  &lt;/div&gt;&#xA;&lt;/div&gt;&#xA;&lt;div class=&#34;music-item&#34; data-song-id=&#34;5&#34;&gt;&#xA;  &lt;div class=&#34;music-cover&#34;&gt;&#xA;    &lt;img src=&#34;http://localhost:1313/images/music/其实好好的.jpg&#34; alt=&#34;歌曲封面&#34; onerror=&#34;this.src=&#39;http://localhost:1313/images/music/default-cover.svg&#39;&#34;&gt;&#xA;    &lt;div class=&#34;play-overlay&#34;&gt;&#xA;      &lt;i class=&#34;fas fa-play play-btn&#34;&gt;&lt;/i&gt;&#xA;    &lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-info&#34;&gt;&#xA;    &lt;h3 class=&#34;song-title&#34;&gt;其实好好的&lt;/h3&gt;&#xA;    &lt;p class=&#34;artist-name&#34;&gt;SLY司赖&lt;/p&gt;&#xA;    &lt;div class=&#34;song-meta&#34;&gt;&#xA;      &lt;span class=&#34;duration&#34;&gt;03:23&lt;/span&gt;&#xA;      &lt;span class=&#34;release-date&#34;&gt;2023-05-15&lt;/span&gt;&#xA;    &lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-actions&#34;&gt;&#xA;    &lt;div class=&#34;netease-player-inline&#34;&gt;&#xA;      &lt;iframe frameborder=&#34;no&#34; border=&#34;0&#34; marginwidth=&#34;0&#34; marginheight=&#34;0&#34; width=330 height=86 src=&#34;//music.163.com/outchain/player?type=2&amp;id=2047634879&amp;auto=0&amp;height=66&#34;&gt;&lt;/iframe&gt;&#xA;    &lt;/div&gt;&#xA;    &lt;a href=&#34;https://music.163.com/song?id=2047634879&amp;uct2=U2FsdGVkX19Dihso1rD9PnKIxPnlJ5SqS8GLNFxCRsQ=&#xA;      &lt;i class=&#34;fas fa-external-link-alt&#34;&gt;&lt;/i&gt; 在网易云音乐中打开&#xA;    &lt;/a&gt;&#xA;  &lt;/div&gt;&#xA;&lt;/div&gt;&#xA;&lt;div class=&#34;music-item&#34; data-song-id=&#34;6&#34;&gt;&#xA;  &lt;div class=&#34;music-cover&#34;&gt;&#xA;    &lt;img src=&#34;http://localhost:1313/images/music/让故事继续.jpg&#34; alt=&#34;歌曲封面&#34; onerror=&#34;this.src=&#39;http://localhost:1313/images/music/default-cover.svg&#39;&#34;&gt;&#xA;    &lt;div class=&#34;play-overlay&#34;&gt;&#xA;      &lt;i class=&#34;fas fa-play play-btn&#34;&gt;&lt;/i&gt;&#xA;    &lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-info&#34;&gt;&#xA;    &lt;h3 class=&#34;song-title&#34;&gt;让故事继续&lt;/h3&gt;&#xA;    &lt;p class=&#34;artist-name&#34;&gt;SLY司赖&lt;/p&gt;&#xA;    &lt;div class=&#34;song-meta&#34;&gt;&#xA;      &lt;span class=&#34;duration&#34;&gt;02:36&lt;/span&gt;&#xA;      &lt;span class=&#34;release-date&#34;&gt;2023-05-09&lt;/span&gt;&#xA;    &lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;  &lt;div class=&#34;music-actions&#34;&gt;&#xA;    &lt;div class=&#34;netease-player-inline&#34;&gt;&#xA;      &lt;iframe frameborder=&#34;no&#34; border=&#34;0&#34; marginwidth=&#34;0&#34; marginheight=&#34;0&#34; width=330 height=86 src=&#34;//music.163.com/outchain/player?type=2&amp;id=2046186641&amp;auto=0&amp;height=66&#34;&gt;&lt;/iframe&gt;&#xA;    &lt;/div&gt;&#xA;    &lt;a href=&#34;https://music.163.com/song?id=2046186641&amp;uct2=U2FsdGVkX19S5TAvvLePjnIKmc5BuG5XlKehZKSrhaw=&#xA;      &lt;i class=&#34;fas fa-external-link-alt&#34;&gt;&lt;/i&gt; 在网易云音乐中打开&#xA;    &lt;/a&gt;&#xA;  &lt;/div&gt;&#xA;&lt;/div&gt;&#xA;  &lt;/div&gt;&#xA;&lt;/div&gt;&#xA;&lt;!-- 网易云音乐播放器嵌入 --&gt;&#xA;&lt;!-- &lt;div class=&#34;netease-player-section&#34;&gt;&#xA;  &lt;h2&gt;🎧 完整播放列表&lt;/h2&gt;&#xA;  &lt;p&gt;我的网易云音乐作品集，包含所有原创歌曲：&lt;/p&gt;&#xA;&#xA;  &lt;!-- 如果您有歌单，可以使用歌单播放器 --&gt;&#xA;  &lt;!-- &lt;iframe frameborder=&#34;no&#34; border=&#34;0&#34; marginwidth=&#34;0&#34; marginheight=&#34;0&#34; width=&#34;100%&#34; height=&#34;450&#34;&#xA;          src=&#34;//music.163.com/outchain/player?type=0&amp;id=YOUR_PLAYLIST_ID&amp;auto=0&amp;height=430&#34;&gt;&#xA;  &lt;/iframe&gt; --&gt;&#xA;  &lt;!-- 或者展示单个歌曲的大播放器 --&gt;&#xA;  &lt;div class=&#34;featured-song&#34;&gt;&#xA;    &lt;h3&gt;🌟 推荐歌曲：我想&lt;/h3&gt;&#xA;    &lt;iframe frameborder=&#34;no&#34; border=&#34;0&#34; marginwidth=&#34;0&#34; marginheight=&#34;0&#34; width=&#34;100%&#34; height=&#34;150&#34; src=&#34;//music.163.com/outchain/player?type=2&amp;id=2606387759&amp;auto=0&amp;height=120&#34;&gt;&lt;/iframe&gt;&#xA;  &lt;/div&gt;&#xA;&lt;/div&gt; --&gt;&#xA;&lt;hr&gt;&#xA;&lt;h2 id=&#34;-关于我的音乐&#34;&gt;🎤 关于我的音乐&lt;/h2&gt;&#xA;&lt;p&gt;我热爱音乐创作，擅长将生活中的感悟融入旋律中。我的音乐风格多样，从R&amp;amp;B到嘻哈，每一首作品都是我内心世界的真实表达。&lt;/p&gt;</description>
    </item>
    <item>
      <title>旅行摄影技巧分享</title>
      <link>http://localhost:1313/blogs/travel-photography-tips/</link>
      <pubDate>Sun, 15 Dec 2024 12:00:00 +0800</pubDate>
      <guid>http://localhost:1313/blogs/travel-photography-tips/</guid>
      <description>&lt;h1 id=&#34;旅行摄影技巧分享&#34;&gt;旅行摄影技巧分享&lt;/h1&gt;&#xA;&lt;h2 id=&#34;前言&#34;&gt;前言&lt;/h2&gt;&#xA;&lt;p&gt;旅行和摄影是完美的组合。通过镜头记录旅途中的美好瞬间，不仅能保存珍贵的回忆，还能与他人分享你眼中的世界。&lt;/p&gt;&#xA;&lt;h2 id=&#34;基础设备准备&#34;&gt;基础设备准备&lt;/h2&gt;&#xA;&lt;h3 id=&#34;相机选择&#34;&gt;相机选择&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;单反相机&lt;/strong&gt;：画质优秀，镜头选择多样&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;微单相机&lt;/strong&gt;：轻便便携，画质接近单反&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;手机&lt;/strong&gt;：随时可用，后期处理方便&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;必备配件&#34;&gt;必备配件&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;三脚架&lt;/strong&gt;：稳定拍摄，长曝光必备&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;偏振镜&lt;/strong&gt;：减少反光，增强色彩饱和度&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;备用电池&lt;/strong&gt;：避免关键时刻没电&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;存储卡&lt;/strong&gt;：足够的存储空间&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h2 id=&#34;构图技巧&#34;&gt;构图技巧&lt;/h2&gt;&#xA;&lt;h3 id=&#34;三分法则&#34;&gt;三分法则&lt;/h3&gt;&#xA;&lt;p&gt;将画面分为九个相等的部分，将主体放在交叉点上，让照片更有视觉冲击力。&lt;/p&gt;&#xA;&lt;h3 id=&#34;引导线&#34;&gt;引导线&lt;/h3&gt;&#xA;&lt;p&gt;利用道路、河流、栏杆等线条引导观者的视线到主体。&lt;/p&gt;&#xA;&lt;h3 id=&#34;前景中景后景&#34;&gt;前景、中景、后景&lt;/h3&gt;&#xA;&lt;p&gt;通过层次感让照片更有深度和立体感。&lt;/p&gt;&#xA;&lt;h2 id=&#34;光线运用&#34;&gt;光线运用&lt;/h2&gt;&#xA;&lt;h3 id=&#34;黄金时间&#34;&gt;黄金时间&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;日出后1小时&lt;/strong&gt;：柔和的暖光&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;日落前1小时&lt;/strong&gt;：温暖的金色光线&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;蓝调时间&#34;&gt;蓝调时间&lt;/h3&gt;&#xA;&lt;p&gt;日落后的30分钟，天空呈现美丽的蓝色调。&lt;/p&gt;&#xA;&lt;h2 id=&#34;不同场景的拍摄技巧&#34;&gt;不同场景的拍摄技巧&lt;/h2&gt;&#xA;&lt;h3 id=&#34;风景摄影&#34;&gt;风景摄影&lt;/h3&gt;&#xA;&lt;pre tabindex=&#34;0&#34;&gt;&lt;code&gt;设置建议：&#xA;- 光圈：f/8-f/11 (获得最佳锐度)&#xA;- ISO：100-400 (保持低噪点)&#xA;- 焦距：广角镜头 (14-35mm)&#xA;&lt;/code&gt;&lt;/pre&gt;&lt;h3 id=&#34;人文摄影&#34;&gt;人文摄影&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;尊重当地文化和习俗&lt;/li&gt;&#xA;&lt;li&gt;获得拍摄许可&lt;/li&gt;&#xA;&lt;li&gt;捕捉自然的表情和动作&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;建筑摄影&#34;&gt;建筑摄影&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;寻找独特的角度&lt;/li&gt;&#xA;&lt;li&gt;注意线条和几何形状&lt;/li&gt;&#xA;&lt;li&gt;利用对称和重复元素&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h2 id=&#34;后期处理技巧&#34;&gt;后期处理技巧&lt;/h2&gt;&#xA;&lt;h3 id=&#34;基础调整&#34;&gt;基础调整&lt;/h3&gt;&#xA;&lt;ol&gt;&#xA;&lt;li&gt;&lt;strong&gt;曝光补偿&lt;/strong&gt;：调整整体亮度&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;对比度&lt;/strong&gt;：增强画面层次&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;饱和度&lt;/strong&gt;：适度增强色彩&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;锐化&lt;/strong&gt;：提升细节清晰度&lt;/li&gt;&#xA;&lt;/ol&gt;&#xA;&lt;h3 id=&#34;常用软件&#34;&gt;常用软件&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;Lightroom&lt;/strong&gt;：RAW文件处理专家&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;Photoshop&lt;/strong&gt;：强大的图像编辑工具&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;Snapseed&lt;/strong&gt;：手机端的专业修图应用&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h2 id=&#34;实用建议&#34;&gt;实用建议&lt;/h2&gt;&#xA;&lt;h3 id=&#34;拍摄前准备&#34;&gt;拍摄前准备&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;研究目的地的最佳拍摄时间和地点&lt;/li&gt;&#xA;&lt;li&gt;查看天气预报&lt;/li&gt;&#xA;&lt;li&gt;准备拍摄清单&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;拍摄中注意事项&#34;&gt;拍摄中注意事项&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;多角度尝试&lt;/li&gt;&#xA;&lt;li&gt;不要只拍&amp;quot;到此一游&amp;quot;照&lt;/li&gt;&#xA;&lt;li&gt;记录旅行过程，不只是景点&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;安全第一&#34;&gt;安全第一&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;注意人身和设备安全&lt;/li&gt;&#xA;&lt;li&gt;不要为了拍照冒险&lt;/li&gt;&#xA;&lt;li&gt;备份重要照片&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h2 id=&#34;总结&#34;&gt;总结&lt;/h2&gt;&#xA;&lt;p&gt;旅行摄影不仅仅是技术的展示，更是情感的表达。通过不断练习和尝试，你会发现自己独特的摄影风格。记住，最好的相机就是你手中的那一台，最重要的是用心去观察和感受这个美丽的世界。&lt;/p&gt;</description>
    </item>
    <item>
      <title>机器学习入门指南</title>
      <link>http://localhost:1313/blogs/machine-learning-intro/</link>
      <pubDate>Sun, 15 Dec 2024 11:00:00 +0800</pubDate>
      <guid>http://localhost:1313/blogs/machine-learning-intro/</guid>
      <description>&lt;h1 id=&#34;机器学习入门指南&#34;&gt;机器学习入门指南&lt;/h1&gt;&#xA;&lt;h2 id=&#34;什么是机器学习&#34;&gt;什么是机器学习？&lt;/h2&gt;&#xA;&lt;p&gt;机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。通过算法和统计模型，机器学习系统可以从数据中学习模式并做出预测。&lt;/p&gt;&#xA;&lt;h2 id=&#34;机器学习的类型&#34;&gt;机器学习的类型&lt;/h2&gt;&#xA;&lt;h3 id=&#34;1-监督学习-supervised-learning&#34;&gt;1. 监督学习 (Supervised Learning)&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;定义&lt;/strong&gt;：使用标记的训练数据来学习输入和输出之间的映射&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;应用&lt;/strong&gt;：分类、回归&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;例子&lt;/strong&gt;：邮件垃圾分类、房价预测&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;2-无监督学习-unsupervised-learning&#34;&gt;2. 无监督学习 (Unsupervised Learning)&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;定义&lt;/strong&gt;：从未标记的数据中发现隐藏的模式&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;应用&lt;/strong&gt;：聚类、降维&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;例子&lt;/strong&gt;：客户分群、异常检测&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;3-强化学习-reinforcement-learning&#34;&gt;3. 强化学习 (Reinforcement Learning)&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;定义&lt;/strong&gt;：通过与环境交互来学习最优行为&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;应用&lt;/strong&gt;：游戏AI、自动驾驶&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;例子&lt;/strong&gt;：AlphaGo、自动驾驶汽车&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h2 id=&#34;常用算法&#34;&gt;常用算法&lt;/h2&gt;&#xA;&lt;h3 id=&#34;线性回归&#34;&gt;线性回归&lt;/h3&gt;&#xA;&lt;div class=&#34;highlight&#34;&gt;&lt;pre tabindex=&#34;0&#34; style=&#34;color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;&#34;&gt;&lt;code class=&#34;language-python&#34; data-lang=&#34;python&#34;&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&lt;span style=&#34;color:#f92672&#34;&gt;from&lt;/span&gt; sklearn.linear_model &lt;span style=&#34;color:#f92672&#34;&gt;import&lt;/span&gt; LinearRegression&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&lt;span style=&#34;color:#f92672&#34;&gt;import&lt;/span&gt; numpy &lt;span style=&#34;color:#66d9ef&#34;&gt;as&lt;/span&gt; np&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&lt;span style=&#34;color:#75715e&#34;&gt;# 创建示例数据&lt;/span&gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;X &lt;span style=&#34;color:#f92672&#34;&gt;=&lt;/span&gt; np&lt;span style=&#34;color:#f92672&#34;&gt;.&lt;/span&gt;array([[&lt;span style=&#34;color:#ae81ff&#34;&gt;1&lt;/span&gt;], [&lt;span style=&#34;color:#ae81ff&#34;&gt;2&lt;/span&gt;], [&lt;span style=&#34;color:#ae81ff&#34;&gt;3&lt;/span&gt;], [&lt;span style=&#34;color:#ae81ff&#34;&gt;4&lt;/span&gt;], [&lt;span style=&#34;color:#ae81ff&#34;&gt;5&lt;/span&gt;]])&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;y &lt;span style=&#34;color:#f92672&#34;&gt;=&lt;/span&gt; np&lt;span style=&#34;color:#f92672&#34;&gt;.&lt;/span&gt;array([&lt;span style=&#34;color:#ae81ff&#34;&gt;2&lt;/span&gt;, &lt;span style=&#34;color:#ae81ff&#34;&gt;4&lt;/span&gt;, &lt;span style=&#34;color:#ae81ff&#34;&gt;6&lt;/span&gt;, &lt;span style=&#34;color:#ae81ff&#34;&gt;8&lt;/span&gt;, &lt;span style=&#34;color:#ae81ff&#34;&gt;10&lt;/span&gt;])&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&lt;span style=&#34;color:#75715e&#34;&gt;# 训练模型&lt;/span&gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;model &lt;span style=&#34;color:#f92672&#34;&gt;=&lt;/span&gt; LinearRegression()&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;model&lt;span style=&#34;color:#f92672&#34;&gt;.&lt;/span&gt;fit(X, y)&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&lt;span style=&#34;color:#75715e&#34;&gt;# 预测&lt;/span&gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;prediction &lt;span style=&#34;color:#f92672&#34;&gt;=&lt;/span&gt; model&lt;span style=&#34;color:#f92672&#34;&gt;.&lt;/span&gt;predict([[&lt;span style=&#34;color:#ae81ff&#34;&gt;6&lt;/span&gt;]])&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;print(&lt;span style=&#34;color:#e6db74&#34;&gt;f&lt;/span&gt;&lt;span style=&#34;color:#e6db74&#34;&gt;&amp;#34;预测结果: &lt;/span&gt;&lt;span style=&#34;color:#e6db74&#34;&gt;{&lt;/span&gt;prediction&lt;span style=&#34;color:#e6db74&#34;&gt;}&lt;/span&gt;&lt;span style=&#34;color:#e6db74&#34;&gt;&amp;#34;&lt;/span&gt;)&#xA;&lt;/span&gt;&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&lt;/div&gt;&lt;h3 id=&#34;决策树&#34;&gt;决策树&lt;/h3&gt;&#xA;&lt;p&gt;决策树是一种直观的算法，通过一系列if-else条件来做决策。&lt;/p&gt;&#xA;&lt;h2 id=&#34;学习路径&#34;&gt;学习路径&lt;/h2&gt;&#xA;&lt;ol&gt;&#xA;&lt;li&gt;&lt;strong&gt;数学基础&lt;/strong&gt;：线性代数、概率论、统计学&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;编程语言&lt;/strong&gt;：Python或R&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;核心库&lt;/strong&gt;：NumPy、Pandas、Scikit-learn&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;实践项目&lt;/strong&gt;：从简单的数据集开始&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;深度学习&lt;/strong&gt;：TensorFlow或PyTorch&lt;/li&gt;&#xA;&lt;/ol&gt;&#xA;&lt;h2 id=&#34;实际应用&#34;&gt;实际应用&lt;/h2&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;&lt;strong&gt;推荐系统&lt;/strong&gt;：Netflix、Amazon的商品推荐&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;图像识别&lt;/strong&gt;：医疗诊断、自动驾驶&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;自然语言处理&lt;/strong&gt;：机器翻译、聊天机器人&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;金融&lt;/strong&gt;：风险评估、算法交易&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h2 id=&#34;总结&#34;&gt;总结&lt;/h2&gt;&#xA;&lt;p&gt;机器学习是一个快速发展的领域，具有巨大的应用潜力。从基础概念开始，通过实践项目逐步提高技能，是学习机器学习的最佳方式。&lt;/p&gt;</description>
    </item>
    <item>
      <title>Web开发基础</title>
      <link>http://localhost:1313/blogs/web-development-basics/</link>
      <pubDate>Sun, 15 Dec 2024 10:00:00 +0800</pubDate>
      <guid>http://localhost:1313/blogs/web-development-basics/</guid>
      <description>&lt;h1 id=&#34;web开发基础指南&#34;&gt;Web开发基础指南&lt;/h1&gt;&#xA;&lt;h2 id=&#34;什么是web开发&#34;&gt;什么是Web开发？&lt;/h2&gt;&#xA;&lt;p&gt;Web开发是创建和维护网站的过程。它包括网页设计、网页内容开发、客户端/服务器端脚本和网络安全配置等方面。&lt;/p&gt;&#xA;&lt;h2 id=&#34;前端技术栈&#34;&gt;前端技术栈&lt;/h2&gt;&#xA;&lt;h3 id=&#34;html-超文本标记语言&#34;&gt;HTML (超文本标记语言)&lt;/h3&gt;&#xA;&lt;p&gt;HTML是网页的骨架，定义了网页的结构和内容。&lt;/p&gt;&#xA;&lt;div class=&#34;highlight&#34;&gt;&lt;pre tabindex=&#34;0&#34; style=&#34;color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;&#34;&gt;&lt;code class=&#34;language-html&#34; data-lang=&#34;html&#34;&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&lt;span style=&#34;color:#75715e&#34;&gt;&amp;lt;!DOCTYPE html&amp;gt;&lt;/span&gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&amp;lt;&lt;span style=&#34;color:#f92672&#34;&gt;html&lt;/span&gt;&amp;gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&amp;lt;&lt;span style=&#34;color:#f92672&#34;&gt;head&lt;/span&gt;&amp;gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;    &amp;lt;&lt;span style=&#34;color:#f92672&#34;&gt;title&lt;/span&gt;&amp;gt;我的第一个网页&amp;lt;/&lt;span style=&#34;color:#f92672&#34;&gt;title&lt;/span&gt;&amp;gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&amp;lt;/&lt;span style=&#34;color:#f92672&#34;&gt;head&lt;/span&gt;&amp;gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&amp;lt;&lt;span style=&#34;color:#f92672&#34;&gt;body&lt;/span&gt;&amp;gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;    &amp;lt;&lt;span style=&#34;color:#f92672&#34;&gt;h1&lt;/span&gt;&amp;gt;欢迎来到我的网站&amp;lt;/&lt;span style=&#34;color:#f92672&#34;&gt;h1&lt;/span&gt;&amp;gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;    &amp;lt;&lt;span style=&#34;color:#f92672&#34;&gt;p&lt;/span&gt;&amp;gt;这是一个段落。&amp;lt;/&lt;span style=&#34;color:#f92672&#34;&gt;p&lt;/span&gt;&amp;gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&amp;lt;/&lt;span style=&#34;color:#f92672&#34;&gt;body&lt;/span&gt;&amp;gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&amp;lt;/&lt;span style=&#34;color:#f92672&#34;&gt;html&lt;/span&gt;&amp;gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&lt;/div&gt;&lt;h3 id=&#34;css-层叠样式表&#34;&gt;CSS (层叠样式表)&lt;/h3&gt;&#xA;&lt;p&gt;CSS负责网页的样式和布局。&lt;/p&gt;&#xA;&lt;div class=&#34;highlight&#34;&gt;&lt;pre tabindex=&#34;0&#34; style=&#34;color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;&#34;&gt;&lt;code class=&#34;language-css&#34; data-lang=&#34;css&#34;&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&lt;span style=&#34;color:#f92672&#34;&gt;body&lt;/span&gt; {&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;    &lt;span style=&#34;color:#66d9ef&#34;&gt;font-family&lt;/span&gt;: Arial, &lt;span style=&#34;color:#66d9ef&#34;&gt;sans-serif&lt;/span&gt;;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;    &lt;span style=&#34;color:#66d9ef&#34;&gt;margin&lt;/span&gt;: &lt;span style=&#34;color:#ae81ff&#34;&gt;0&lt;/span&gt;;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;    &lt;span style=&#34;color:#66d9ef&#34;&gt;padding&lt;/span&gt;: &lt;span style=&#34;color:#ae81ff&#34;&gt;20&lt;/span&gt;&lt;span style=&#34;color:#66d9ef&#34;&gt;px&lt;/span&gt;;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;}&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&lt;span style=&#34;color:#f92672&#34;&gt;h1&lt;/span&gt; {&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;    &lt;span style=&#34;color:#66d9ef&#34;&gt;color&lt;/span&gt;: &lt;span style=&#34;color:#ae81ff&#34;&gt;#333&lt;/span&gt;;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;    &lt;span style=&#34;color:#66d9ef&#34;&gt;text-align&lt;/span&gt;: &lt;span style=&#34;color:#66d9ef&#34;&gt;center&lt;/span&gt;;&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;}&#xA;&lt;/span&gt;&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&lt;/div&gt;&lt;h3 id=&#34;javascript&#34;&gt;JavaScript&lt;/h3&gt;&#xA;&lt;p&gt;JavaScript为网页添加交互性和动态功能。&lt;/p&gt;&#xA;&lt;div class=&#34;highlight&#34;&gt;&lt;pre tabindex=&#34;0&#34; style=&#34;color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;&#34;&gt;&lt;code class=&#34;language-javascript&#34; data-lang=&#34;javascript&#34;&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;&lt;span style=&#34;color:#66d9ef&#34;&gt;function&lt;/span&gt; &lt;span style=&#34;color:#a6e22e&#34;&gt;greetUser&lt;/span&gt;() {&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;    &lt;span style=&#34;color:#66d9ef&#34;&gt;const&lt;/span&gt; &lt;span style=&#34;color:#a6e22e&#34;&gt;name&lt;/span&gt; &lt;span style=&#34;color:#f92672&#34;&gt;=&lt;/span&gt; &lt;span style=&#34;color:#a6e22e&#34;&gt;prompt&lt;/span&gt;(&lt;span style=&#34;color:#e6db74&#34;&gt;&amp;#34;请输入您的姓名：&amp;#34;&lt;/span&gt;);&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;    &lt;span style=&#34;color:#a6e22e&#34;&gt;alert&lt;/span&gt;(&lt;span style=&#34;color:#e6db74&#34;&gt;&amp;#34;你好，&amp;#34;&lt;/span&gt; &lt;span style=&#34;color:#f92672&#34;&gt;+&lt;/span&gt; &lt;span style=&#34;color:#a6e22e&#34;&gt;name&lt;/span&gt; &lt;span style=&#34;color:#f92672&#34;&gt;+&lt;/span&gt; &lt;span style=&#34;color:#e6db74&#34;&gt;&amp;#34;！欢迎访问我们的网站！&amp;#34;&lt;/span&gt;);&#xA;&lt;/span&gt;&lt;/span&gt;&lt;span style=&#34;display:flex;&#34;&gt;&lt;span&gt;}&#xA;&lt;/span&gt;&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;&lt;/div&gt;&lt;h2 id=&#34;学习路径&#34;&gt;学习路径&lt;/h2&gt;&#xA;&lt;ol&gt;&#xA;&lt;li&gt;&lt;strong&gt;HTML基础&lt;/strong&gt; - 学习标签、属性和语义化&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;CSS基础&lt;/strong&gt; - 掌握选择器、布局和响应式设计&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;JavaScript基础&lt;/strong&gt; - 理解变量、函数和DOM操作&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;框架学习&lt;/strong&gt; - React、Vue或Angular&lt;/li&gt;&#xA;&lt;li&gt;&lt;strong&gt;后端技术&lt;/strong&gt; - Node.js、Python或其他服务器端语言&lt;/li&gt;&#xA;&lt;/ol&gt;&#xA;&lt;h2 id=&#34;总结&#34;&gt;总结&lt;/h2&gt;&#xA;&lt;p&gt;Web开发是一个不断发展的领域，需要持续学习和实践。从基础的HTML、CSS和JavaScript开始，逐步深入到更复杂的框架和技术。&lt;/p&gt;</description>
    </item>
    <item>
      <title>愿原力与你同在：星球大战日</title>
      <link>http://localhost:1313/blogs/fourth/</link>
      <pubDate>Thu, 01 Feb 2024 04:56:02 +0530</pubDate>
      <guid>http://localhost:1313/blogs/fourth/</guid>
      <description>&lt;h1 id=&#34;may-the-fourth-be-with-you-a-galactic-pun&#34;&gt;May the Fourth Be with You: A Galactic Pun&lt;/h1&gt;&#xA;&lt;h2 id=&#34;introduction&#34;&gt;Introduction&lt;/h2&gt;&#xA;&lt;p&gt;Greetings, Earthlings and intergalactic travelers! Today, in my fourth post, we venture into a galaxy far, far away to explore a day cherished by rebels, Jedi, and starship enthusiasts alike — May the Fourth. Yes, you&amp;rsquo;ve guessed it! It&amp;rsquo;s the unofficial Star Wars Day, and we&amp;rsquo;re here to unravel an elaborate joke that has transcended space and time.&lt;/p&gt;&#xA;&lt;h2 id=&#34;the-origin-of-the-pun&#34;&gt;The Origin of the Pun&lt;/h2&gt;&#xA;&lt;p&gt;It all started a long time ago in a galaxy&amp;hellip; well, not too far away. The date was May 4, 1979, when Margaret Thatcher became the first woman Prime Minister of the United Kingdom. Her political party placed a congratulatory ad in the London Evening News that read, &amp;ldquo;May the Fourth Be with You, Maggie. Congratulations.&amp;rdquo; Little did they know, they had just coined a phrase that would become a rallying cry for Star Wars fans for decades to come.&lt;/p&gt;</description>
    </item>
    <item>
      <title>卡尔·萨根的暗淡蓝点</title>
      <link>http://localhost:1313/blogs/third/</link>
      <pubDate>Thu, 01 Feb 2024 04:55:59 +0530</pubDate>
      <guid>http://localhost:1313/blogs/third/</guid>
      <description>&lt;h1 id=&#34;reflecting-on-carl-sagans-pale-blue-dot&#34;&gt;Reflecting on Carl Sagan&amp;rsquo;s Pale Blue Dot&lt;/h1&gt;&#xA;&lt;p&gt;&lt;img src=&#34;https://images.unsplash.com/photo-1572442350603-6cf8355670dd?q=80&amp;amp;w=800&amp;amp;auto=format&amp;amp;fit=crop&amp;amp;ixlib=rb-4.0.3&amp;amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&#34; alt=&#34;Image of Space&#34;&gt;&lt;/p&gt;&#xA;&lt;p&gt;&lt;em&gt;Photo by &lt;a href=&#34;%22https://unsplash.com/@juniperphoton?utm_content=creditCopyText&amp;amp;utm_medium=referral&amp;amp;utm_source=unsplash%22&#34;&gt;JuniperPhoton&lt;/a&gt;&lt;/em&gt;&lt;/p&gt;&#xA;&lt;h2 id=&#34;introduction&#34;&gt;Introduction&lt;/h2&gt;&#xA;&lt;p&gt;In 1990, Voyager 1, a tiny spacecraft hurled into the vast expanse of space, turned its camera around at the request of astronomer Carl Sagan and took a photograph of Earth from a distance of about 3.7 billion miles. This image, known as the &amp;ldquo;Pale Blue Dot,&amp;rdquo; is a humbling depiction of our planet — a tiny, fragile speck suspended in a sunbeam.&lt;/p&gt;</description>
    </item>
    <item>
      <title>About</title>
      <link>http://localhost:1313/about/</link>
      <pubDate>Sun, 14 Jan 2024 07:07:07 +0100</pubDate>
      <guid>http://localhost:1313/about/</guid>
      <description>&lt;p&gt;Hi, I&amp;rsquo;m Sun Longyu from Nanchang, Jiangxi. Currently, I am a master student in Guangzhou University, majoring in Computer Technology in the School of Cyberspace Security, a member of &lt;a href=&#34;blockchain-neu.com&#34;&gt;Blockchain and Reputation Systems Research Group&lt;/a&gt;.From 2019-2023, I studied in Jiangxi University of Science and Technology, and I was awarded the National Scholarship, Academician Zhang Wenhai Scholarship, and Haopeng Science and Technology Scholarship, etc., and I received my Bachelor&amp;rsquo;s Degree of Engineering in June 2023.I love to write music when I study.&lt;/p&gt;</description>
    </item>
    <item>
      <title>GoaTech&#39;s Rise</title>
      <link>http://localhost:1313/vlogs/</link>
      <pubDate>Sun, 02 Oct 2016 22:55:05 -0400</pubDate>
      <guid>http://localhost:1313/vlogs/</guid>
      <description>&lt;p&gt;&lt;strong&gt;Notes&lt;/strong&gt;: This article originally appeared on &lt;a href=&#34;https://incirclemedia.com&#34;&gt;GoaTech Insights&lt;/a&gt;.&lt;/p&gt;&#xA;&lt;p&gt;GoaTech Insights has always been more than just another tech industry blog. Our mission is to be an integral part of the tech community, offering an in-depth and nuanced look at the latest developments in the field. Our ethos is encapsulated in our tagline: &amp;ldquo;Innovate and Inspire.&amp;rdquo; We believe that a tech news platform should not only be informative but also inspire its readers. We achieve this by being at the heart of the industry we cover. As a part of the dynamic tech ecosystem, GoaTech Insights is constantly evolving.&lt;/p&gt;</description>
    </item>
  </channel>
</rss>
