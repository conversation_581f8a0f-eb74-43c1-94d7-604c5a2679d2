<!DOCTYPE html>
<html lang="zh-cn">

    <head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>

        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="content-type" content="text/html; charset=utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">

        
        <meta name="description" content="Simple minimalist theme">
        
        <meta name="keywords" content="minimalist,blog,goa,hugo,developer">

        <title>
              Sun longyu - 我的音乐作品 
        </title>

        <meta name="generator" content="Hugo 0.147.7">

        
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/default.min.css">
        

        <link rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400&family=Roboto+Slab:wght@400;700&family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700" />
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
            integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC"
            crossorigin="anonymous" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
        <link rel="stylesheet" href="http://localhost:1313/css/main.css">
        <link rel="stylesheet" href="http://localhost:1313/css/custom.css">

        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
        <link rel="manifest" href="/site.webmanifest">
        <meta name="theme-color" content="#ffffff"></head>

    <body lang="zh-cn">
        <div class="container my-auto">

<div class="music-page-wrapper">
  <header class="text-start title">
  <h1 class="title">我的音乐作品</h1>
</header>

<section id="category-pane" class="meta">
  
  <div class="d-flex flex-column flex-md-row justify-content-between">
    <h6 class="text-start meta">
       PUBLISHED ON DEC 15, 2024
      
      / 1 MIN READ
      
      
      
      —
      
      
      <a class="meta"
        href="/categories/%E9%9F%B3%E4%B9%90">音乐</a>
      
      
      
    </h6>
  </div>
  
</section>

<section id="content-pane">
  <div class="text-justify content">
    
    <h1 id="-我的音乐世界">🎵 我的音乐世界</h1>
<p>欢迎来到我的音乐空间！这里收录了我在网易云音乐平台上发布的原创作品。每一首歌都承载着不同的情感和故事，希望能与你产生共鸣。</p>
<hr>
<div class="music-player-container">
  <div class="playlist-header">
    <h2>🎼 我的原创作品集</h2>
    <p class="playlist-description">点击播放按钮即可在线试听</p>
  </div>
  <div class="music-list">
<div class="music-item" data-song-id="1">
  <div class="music-cover">
    <img src="/images/music/我想.png" alt="歌曲封面" onerror="this.src='/images/music/default-cover.svg'">
    <div class="play-overlay">
      <i class="fas fa-play play-btn"></i>
    </div>
  </div>
  <div class="music-info">
    <h3 class="song-title">我想</h3>
    <p class="artist-name">SLY司赖</p>
    <div class="song-meta">
      <span class="duration">02:47</span>
      <span class="release-date">2024-07-11</span>
    </div>
  </div>
  <div class="music-actions">
    <div class="netease-player-inline">
      <iframe frameborder="no" border="0" marginwidth="0" marginheight="0" width=330 height="86" src="//music.163.com/outchain/player?type=2&id=2606387759&auto=0&height=66"></iframe>
    </div>
    <a href="https://music.163.com/song?id=2606387759&uct2=U2FsdGVkX18+sj7PYQ7JjdFLjyQ/xb6+8XIe9N1RJnE=" target="_blank" class="netease-link-small">
      <i class="fas fa-external-link-alt"></i> 在网易云音乐中打开
    </a>
  </div>
</div>
<div class="music-item" data-song-id="2">
  <div class="music-cover">
    <img src="/images/music/我也想在你想我的时候想着你.jpg" alt="歌曲封面" onerror="this.src='/images/music/default-cover.svg'">
    <div class="play-overlay">
      <i class="fas fa-play play-btn"></i>
    </div>
  </div>
  <div class="music-info">
    <h3 class="song-title">我也想在你想我的时候想着你</h3>
    <p class="artist-name">SLY司赖</p>
    <div class="song-meta">
      <span class="duration">02:54</span>
      <span class="release-date">2024-02-24</span>
    </div>
  </div>
  <div class="music-actions">
    <div class="netease-player-inline">
      <iframe frameborder="no" border="0" marginwidth="0" marginheight="0" width="100%" height="86" src="//music.163.com/outchain/player?type=2&id=2127901953&auto=0&height=66"></iframe>
    </div>
    <a href="https://music.163.com/song?id=2127901953" target="_blank" class="netease-link-small">
      <i class="fas fa-external-link-alt"></i> 在网易云音乐中打开
    </a>
  </div>
</div>
<div class="music-item" data-song-id="3">
  <div class="music-cover">
    <img src="/images/music/想过.jpg" alt="歌曲封面" onerror="this.src='/images/music/default-cover.svg'">
    <div class="play-overlay">
      <i class="fas fa-play play-btn"></i>
    </div>
  </div>
  <div class="music-info">
    <h3 class="song-title">想·过</h3>
    <p class="artist-name">SLY司赖</p>
    <div class="song-meta">
      <span class="duration">03:26</span>
      <span class="release-date">2023-07-11</span>
    </div>
  </div>
  <div class="music-actions">
    <div class="netease-player-inline">
      <iframe frameborder="no" border="0" marginwidth="0" marginheight="0" width=330 height=86 src="//music.163.com/outchain/player?type=2&id=2063028431&auto=0&height=66"></iframe>
    </div>
    <a href="https://music.163.com/song?id=2063028431&uct2=U2FsdGVkX1+UNuLnE/0v7E8tYV0kPpwwZFFHGFneioU=
      <i class="fas fa-external-link-alt"></i> 在网易云音乐中打开
    </a>
  </div>
</div>
<div class="music-item" data-song-id="4">
  <div class="music-cover">
    <img src="/images/music/英雄.jpg" alt="歌曲封面" onerror="this.src='/images/music/default-cover.svg'">
    <div class="play-overlay">
      <i class="fas fa-play play-btn"></i>
    </div>
  </div>
  <div class="music-info">
    <h3 class="song-title">英雄</h3>
    <p class="artist-name">SLY司赖</p>
    <div class="song-meta">
      <span class="duration">02:15</span>
      <span class="release-date">2023-07-05</span>
    </div>
  </div>
  <div class="music-actions">
    <div class="netease-player-inline">
      <iframe frameborder="no" border="0" marginwidth="0" marginheight="0" width=330 height=86 src="//music.163.com/outchain/player?type=2&id=2060963543&auto=0&height=66"></iframe>
    </div>
    <a href="https://music.163.com/song?id=2060963543&uct2=U2FsdGVkX19A3vVO/tUij/lNKYYGGC95lbW0aUaggKw=
      <i class="fas fa-external-link-alt"></i> 在网易云音乐中打开
    </a>
  </div>
</div>
<div class="music-item" data-song-id="5">
  <div class="music-cover">
    <img src="/images/music/其实好好的.jpg" alt="歌曲封面" onerror="this.src='/images/music/default-cover.svg'">
    <div class="play-overlay">
      <i class="fas fa-play play-btn"></i>
    </div>
  </div>
  <div class="music-info">
    <h3 class="song-title">其实好好的</h3>
    <p class="artist-name">SLY司赖</p>
    <div class="song-meta">
      <span class="duration">03:23</span>
      <span class="release-date">2023-05-15</span>
    </div>
  </div>
  <div class="music-actions">
    <div class="netease-player-inline">
      <iframe frameborder="no" border="0" marginwidth="0" marginheight="0" width=330 height=86 src="//music.163.com/outchain/player?type=2&id=2047634879&auto=0&height=66"></iframe>
    </div>
    <a href="https://music.163.com/song?id=2047634879&uct2=U2FsdGVkX19Dihso1rD9PnKIxPnlJ5SqS8GLNFxCRsQ=
      <i class="fas fa-external-link-alt"></i> 在网易云音乐中打开
    </a>
  </div>
</div>
<div class="music-item" data-song-id="6">
  <div class="music-cover">
    <img src="/images/music/让故事继续.jpg" alt="歌曲封面" onerror="this.src='/images/music/default-cover.svg'">
    <div class="play-overlay">
      <i class="fas fa-play play-btn"></i>
    </div>
  </div>
  <div class="music-info">
    <h3 class="song-title">让故事继续</h3>
    <p class="artist-name">SLY司赖</p>
    <div class="song-meta">
      <span class="duration">02:36</span>
      <span class="release-date">2023-05-09</span>
    </div>
  </div>
  <div class="music-actions">
    <div class="netease-player-inline">
      <iframe frameborder="no" border="0" marginwidth="0" marginheight="0" width=330 height=86 src="//music.163.com/outchain/player?type=2&id=2046186641&auto=0&height=66"></iframe>
    </div>
    <a href="https://music.163.com/song?id=2046186641&uct2=U2FsdGVkX19S5TAvvLePjnIKmc5BuG5XlKehZKSrhaw=
      <i class="fas fa-external-link-alt"></i> 在网易云音乐中打开
    </a>
  </div>
</div>
  </div>
</div>
<!-- 网易云音乐播放器嵌入 -->
<!-- <div class="netease-player-section">
  <h2>🎧 完整播放列表</h2>
  <p>我的网易云音乐作品集，包含所有原创歌曲：</p>

  <!-- 如果您有歌单，可以使用歌单播放器 -->
  <!-- <iframe frameborder="no" border="0" marginwidth="0" marginheight="0" width="100%" height="450"
          src="//music.163.com/outchain/player?type=0&id=YOUR_PLAYLIST_ID&auto=0&height=430">
  </iframe> -->
  <!-- 或者展示单个歌曲的大播放器 -->
  <div class="featured-song">
    <h3>🌟 推荐歌曲：我想</h3>
    <iframe frameborder="no" border="0" marginwidth="0" marginheight="0" width="100%" height="150" src="//music.163.com/outchain/player?type=2&id=2606387759&auto=0&height=120"></iframe>
  </div>
</div> -->
<hr>
<h2 id="-关于我的音乐">🎤 关于我的音乐</h2>
<p>我热爱音乐创作，擅长将生活中的感悟融入旋律中。我的音乐风格多样，从R&amp;B到嘻哈，每一首作品都是我内心世界的真实表达。</p>
<h3 id="-创作理念">🎹 创作理念</h3>
<ul>
<li><strong>真实表达</strong>：用音乐记录生活中的点点滴滴</li>
<li><strong>情感共鸣</strong>：希望我的音乐能触动听众的心灵</li>
<li><strong>持续创新</strong>：不断尝试新的音乐风格和制作技巧</li>
</ul>
<h3 id="-在哪里可以听到我的音乐">📱 在哪里可以听到我的音乐</h3>
<ul>
<li><strong>网易云音乐</strong>：<a href="https://music.163.com/#/artist?id=56638256">个人主页</a></li>
</ul>
<!-- - **QQ音乐**：即将上线
- **酷狗音乐**：即将上线 -->
<hr>
<p><em>如果你喜欢我的音乐，欢迎在网易云音乐上关注我，与我交流音乐心得！</em></p>

  </div>
</section>

<section id="tag-pane" class="meta">
  
  <div class="d-flex flex-column flex-md-row justify-content-between">
    <h6 class="text-end meta">
      
      
      
      TAGS:
      
      
      <a class="meta" href="/tags/%E4%BD%9C%E5%93%81%E9%9B%86">作品集</a>,
      
      
      <a class="meta" href="/tags/%E5%8E%9F%E5%88%9B">原创</a>,
      
      
      <a class="meta" href="/tags/%E7%BD%91%E6%98%93%E4%BA%91%E9%9F%B3%E4%B9%90">网易云音乐</a>,
      
      
      <a class="meta" href="/tags/%E9%9F%B3%E4%B9%90">音乐</a>
      
      
      
    </h6>
  </div>
  
</section>
</div>





<section id="menu-pane" class="menu text-center">
  
  <nav aria-label="Page navigation">
    <ul class="pagination justify-content-center">
      
    </ul>
  </nav>
  
  <h4 class="text-center mt-3"><a class="menu-item" href="http://localhost:1313/">home</a></h4>
</section>

<footer class="text-center footer">
  <hr />
  
  <h6 class="text-center copyright">© 2025. SLY. <a href="http://creativecommons.org/licenses/by/3.0/">Some Rights Reserved</a>.</h6>
  
  <h6 class="text-center powered"><a href="https://gohugo.io/">Hugo</a> <a
      href="https://github.com/shenoydotme/hugo-goa">Goa</a> by <a href="https://shenoy.me">shenoydotme</a> and <a
      href="https://incirclemedia.com">Incircle Media</a></h6>
  
  
  <h6>
    <a href="http://localhost:1313/index.xml" aria-label="RSS Feed"><i class="fas fa-rss" aria-hidden="true"></i></a>
  </h6>
  
  

</footer>
</div>



<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>


<script>hljs.highlightAll();</script>




<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
  integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM" crossorigin="anonymous"></script>
<script src="/js/custom.js"></script>
</body>

</html>

<style>
 
.music-page-wrapper {
  min-height: 60vh;
}

 
.music-player-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.playlist-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.music-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.music-item {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.music-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: #667eea;
}

.music-cover {
  position: relative;
  width: 80px;
  height: 80px;
  margin-right: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.music-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.music-cover:hover .play-overlay {
  opacity: 1;
}

.play-btn {
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
}

.music-info {
  flex: 1;
  min-width: 0;
}

.song-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
}

.artist-name {
  margin: 0 0 0.8rem 0;
  color: #667eea;
  font-weight: 500;
}

.song-description {
  margin: 0 0 1rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

.song-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: #999;
}

.netease-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  background: #d33a31;
  color: white !important;
  text-decoration: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.netease-link:hover {
  background: #b8302a;
  transform: translateY(-1px);
}

.netease-player-section {
  margin: 3rem 0;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.netease-player-section iframe {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .music-item {
    flex-direction: column;
    text-align: center;
    padding: 1.2rem;
  }
  
  .music-cover {
    margin-right: 0;
    margin-bottom: 1rem;
    width: 100px;
    height: 100px;
  }
  
  .music-actions {
    margin-left: 0;
    margin-top: 1rem;
  }
}
</style>
