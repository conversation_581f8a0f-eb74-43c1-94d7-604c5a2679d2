<!DOCTYPE html>
<html lang="zh-cn">

    <head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>

        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="content-type" content="text/html; charset=utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">

        
        <meta name="description" content="Simple minimalist theme">
        
        <meta name="keywords" content="minimalist,blog,goa,hugo,developer">

        <title>
              Sun longyu - Web开发基础 
        </title>

        <meta name="generator" content="Hugo 0.147.7">

        
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/default.min.css">
        

        <link rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400&family=Roboto+Slab:wght@400;700&family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700" />
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
            integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC"
            crossorigin="anonymous" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
        <link rel="stylesheet" href="http://localhost:1313/css/main.css">
        <link rel="stylesheet" href="http://localhost:1313/css/custom.css">

        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
        <link rel="manifest" href="/site.webmanifest">
        <meta name="theme-color" content="#ffffff"></head>

    <body lang="zh-cn">
        <div class="container my-auto">

<header class="text-start title">
  <h1 class="title">Web开发基础</h1>
</header>

<section id="category-pane" class="meta">
  
  <div class="d-flex flex-column flex-md-row justify-content-between">
    <h6 class="text-start meta">
       PUBLISHED ON DEC 15, 2024
      
      / 2 MIN READ
      
      
      
      —
      
      
      <a class="meta"
        href="/categories/web%E5%BC%80%E5%8F%91">WEB开发</a>, 
      
      <a class="meta"
        href="/categories/%E7%BC%96%E7%A8%8B">编程</a>
      
      
      
    </h6>
  </div>
  
</section>

<section id="content-pane">
  <div class="text-justify content">
    
    <h1 id="web开发基础指南">Web开发基础指南</h1>
<h2 id="什么是web开发">什么是Web开发？</h2>
<p>Web开发是创建和维护网站的过程。它包括网页设计、网页内容开发、客户端/服务器端脚本和网络安全配置等方面。</p>
<h2 id="前端技术栈">前端技术栈</h2>
<h3 id="html-超文本标记语言">HTML (超文本标记语言)</h3>
<p>HTML是网页的骨架，定义了网页的结构和内容。</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-html" data-lang="html"><span style="display:flex;"><span><span style="color:#75715e">&lt;!DOCTYPE html&gt;</span>
</span></span><span style="display:flex;"><span>&lt;<span style="color:#f92672">html</span>&gt;
</span></span><span style="display:flex;"><span>&lt;<span style="color:#f92672">head</span>&gt;
</span></span><span style="display:flex;"><span>    &lt;<span style="color:#f92672">title</span>&gt;我的第一个网页&lt;/<span style="color:#f92672">title</span>&gt;
</span></span><span style="display:flex;"><span>&lt;/<span style="color:#f92672">head</span>&gt;
</span></span><span style="display:flex;"><span>&lt;<span style="color:#f92672">body</span>&gt;
</span></span><span style="display:flex;"><span>    &lt;<span style="color:#f92672">h1</span>&gt;欢迎来到我的网站&lt;/<span style="color:#f92672">h1</span>&gt;
</span></span><span style="display:flex;"><span>    &lt;<span style="color:#f92672">p</span>&gt;这是一个段落。&lt;/<span style="color:#f92672">p</span>&gt;
</span></span><span style="display:flex;"><span>&lt;/<span style="color:#f92672">body</span>&gt;
</span></span><span style="display:flex;"><span>&lt;/<span style="color:#f92672">html</span>&gt;
</span></span></code></pre></div><h3 id="css-层叠样式表">CSS (层叠样式表)</h3>
<p>CSS负责网页的样式和布局。</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-css" data-lang="css"><span style="display:flex;"><span><span style="color:#f92672">body</span> {
</span></span><span style="display:flex;"><span>    <span style="color:#66d9ef">font-family</span>: Arial, <span style="color:#66d9ef">sans-serif</span>;
</span></span><span style="display:flex;"><span>    <span style="color:#66d9ef">margin</span>: <span style="color:#ae81ff">0</span>;
</span></span><span style="display:flex;"><span>    <span style="color:#66d9ef">padding</span>: <span style="color:#ae81ff">20</span><span style="color:#66d9ef">px</span>;
</span></span><span style="display:flex;"><span>}
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">h1</span> {
</span></span><span style="display:flex;"><span>    <span style="color:#66d9ef">color</span>: <span style="color:#ae81ff">#333</span>;
</span></span><span style="display:flex;"><span>    <span style="color:#66d9ef">text-align</span>: <span style="color:#66d9ef">center</span>;
</span></span><span style="display:flex;"><span>}
</span></span></code></pre></div><h3 id="javascript">JavaScript</h3>
<p>JavaScript为网页添加交互性和动态功能。</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-javascript" data-lang="javascript"><span style="display:flex;"><span><span style="color:#66d9ef">function</span> <span style="color:#a6e22e">greetUser</span>() {
</span></span><span style="display:flex;"><span>    <span style="color:#66d9ef">const</span> <span style="color:#a6e22e">name</span> <span style="color:#f92672">=</span> <span style="color:#a6e22e">prompt</span>(<span style="color:#e6db74">&#34;请输入您的姓名：&#34;</span>);
</span></span><span style="display:flex;"><span>    <span style="color:#a6e22e">alert</span>(<span style="color:#e6db74">&#34;你好，&#34;</span> <span style="color:#f92672">+</span> <span style="color:#a6e22e">name</span> <span style="color:#f92672">+</span> <span style="color:#e6db74">&#34;！欢迎访问我们的网站！&#34;</span>);
</span></span><span style="display:flex;"><span>}
</span></span></code></pre></div><h2 id="学习路径">学习路径</h2>
<ol>
<li><strong>HTML基础</strong> - 学习标签、属性和语义化</li>
<li><strong>CSS基础</strong> - 掌握选择器、布局和响应式设计</li>
<li><strong>JavaScript基础</strong> - 理解变量、函数和DOM操作</li>
<li><strong>框架学习</strong> - React、Vue或Angular</li>
<li><strong>后端技术</strong> - Node.js、Python或其他服务器端语言</li>
</ol>
<h2 id="总结">总结</h2>
<p>Web开发是一个不断发展的领域，需要持续学习和实践。从基础的HTML、CSS和JavaScript开始，逐步深入到更复杂的框架和技术。</p>

  </div>
</section>

<section id="tag-pane" class="meta">
  
  <div class="d-flex flex-column flex-md-row justify-content-between">
    <h6 class="text-end meta">
      
      
      
      TAGS:
      
      
      <a class="meta" href="/tags/css">CSS</a>,
      
      
      <a class="meta" href="/tags/html">HTML</a>,
      
      
      <a class="meta" href="/tags/javascript">JAVASCRIPT</a>,
      
      
      <a class="meta" href="/tags/%E5%89%8D%E7%AB%AF">前端</a>
      
      
      
    </h6>
  </div>
  
</section>







<section id="menu-pane" class="menu text-center">
  
  <nav aria-label="Page navigation">
    <ul class="pagination justify-content-center">
      
      
      <li class="menu-item">
        <a class="menu-item" href="http://localhost:1313/blogs/fourth/">&lt; prev</a>
      </li>
      

      <li class="menu-item">
        
        <span class="menu-item">&nbsp;|&nbsp;</span>
        
        <a class="menu-item" href="/blogs">blogs</a>
        
        <span class="menu-item">&nbsp;|&nbsp;</span>
        
      </li>

      
      <li class="menu-item">
        <a class="menu-item" href="http://localhost:1313/blogs/machine-learning-intro/">next &gt;</a>
      </li>
      
      
    </ul>
  </nav>
  
  <h4 class="text-center mt-3"><a class="menu-item" href="http://localhost:1313/">home</a></h4>
</section>

<footer class="text-center footer">
  <hr />
  
  <h6 class="text-center copyright">© 2024. SLY. <a href="http://creativecommons.org/licenses/by/3.0/">Some Rights Reserved</a>.</h6>
  
  <h6 class="text-center powered"><a href="https://gohugo.io/">Hugo</a> <a
      href="https://github.com/shenoydotme/hugo-goa">Goa</a> by <a href="https://shenoy.me">shenoydotme</a> and <a
      href="https://incirclemedia.com">Incircle Media</a></h6>
  
  
  <h6>
    <a href="http://localhost:1313/index.xml" aria-label="RSS Feed"><i class="fas fa-rss" aria-hidden="true"></i></a>
  </h6>
  
  

</footer>
</div>



<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>


<script>hljs.highlightAll();</script>




<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
  integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM" crossorigin="anonymous"></script>
<script src="/js/custom.js"></script>
</body>

</html>

