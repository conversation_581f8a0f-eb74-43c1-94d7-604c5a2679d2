<!DOCTYPE html>
<html lang="zh-cn">

    <head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>

        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="content-type" content="text/html; charset=utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">

        
        <meta name="description" content="Simple minimalist theme">
        
        <meta name="keywords" content="minimalist,blog,goa,hugo,developer">

        <title>
              Sun longyu - LLM入门 
        </title>

        <meta name="generator" content="Hugo 0.147.7">

        
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/default.min.css">
        

        <link rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400&family=Roboto+Slab:wght@400;700&family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700" />
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
            integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC"
            crossorigin="anonymous" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
        <link rel="stylesheet" href="http://localhost:1313/css/main.css">
        <link rel="stylesheet" href="http://localhost:1313/css/custom.css">

        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
        <link rel="manifest" href="/site.webmanifest">
        <meta name="theme-color" content="#ffffff"></head>

    <body lang="zh-cn">
        <div class="container my-auto">

<header class="text-start title">
  <h1 class="title">LLM入门</h1>
</header>

<section id="category-pane" class="meta">
  
  <div class="d-flex flex-column flex-md-row justify-content-between">
    <h6 class="text-start meta">
       PUBLISHED ON JAN 14, 2024
      
      / 1 MIN READ
      
      
      
      —
      
      
      <a class="meta"
        href="/categories/llm">LLM</a>, 
      
      <a class="meta"
        href="/categories/%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD">人工智能</a>
      
      
      
    </h6>
  </div>
  
</section>

<section id="content-pane">
  <div class="text-justify content">
    
    <h2 id="introduction">Introduction</h2>
<p>Welcome to my first post using Hugo! In this post, I&rsquo;m going to showcase some basic Markdown formatting and discuss why I chose Hugo for my blogging platform.</p>
<p>This is <strong>bold</strong> text, and this is <em>emphasized</em> text.</p>
<h2 id="why-hugo">Why Hugo?</h2>
<p><a href="https://gohugo.io">Hugo</a> is a fast and flexible static site generator, perfect for blogs, portfolios, and even company websites. Here&rsquo;s why I love it:</p>
<ul>
<li><strong>Speed</strong>: Hugo generates pages at an incredibly fast speed.</li>
<li><strong>Flexibility</strong>: You can create any type of website with Hugo.</li>
<li><strong>Community</strong>: The Hugo community is supportive and continuously growing.</li>
</ul>
<h2 id="markdown-basics">Markdown Basics</h2>
<p>Markdown is a lightweight markup language that you can use to add formatting elements to plaintext text documents. Here are some basics:</p>
<ul>
<li><strong>Headers</strong>: Use <code>##</code> for a secondary header, and <code>###</code> for a tertiary header.</li>
<li><strong>Lists</strong>:
<ul>
<li>Unordered lists are created using dashes (<code>-</code>).</li>
<li>Ordered lists simply use numbers.</li>
</ul>
</li>
<li><strong>Links</strong>: To add a <a href="#">link</a>, put the text in square brackets and the URL in parentheses.</li>
<li><strong>Images</strong>: Add images with <code>![Alt text](image-url.jpg)</code>.</li>
<li><strong>Code</strong>: Use backticks to <code>highlight</code> inline code or triple backticks for code blocks.</li>
</ul>
<h2 id="conclusion">Conclusion</h2>
<p>I hope this post gives you a glimpse into what Hugo and Markdown can do. Stay tuned for more content!</p>
<p>Visit the <a href="https://gohugo.io">Hugo</a> website to learn more!</p>

  </div>
</section>

<section id="tag-pane" class="meta">
  
  <div class="d-flex flex-column flex-md-row justify-content-between">
    <h6 class="text-end meta">
      
      
      
      TAGS:
      
      
      <a class="meta" href="/tags/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0">机器学习</a>,
      
      
      <a class="meta" href="/tags/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0">深度学习</a>,
      
      
      <a class="meta" href="/tags/%E8%87%AA%E7%84%B6%E8%AF%AD%E8%A8%80%E5%A4%84%E7%90%86">自然语言处理</a>
      
      
      
    </h6>
  </div>
  
</section>







<section id="menu-pane" class="menu text-center">
  
  <nav aria-label="Page navigation">
    <ul class="pagination justify-content-center">
      
      

      <li class="menu-item">
        
        <a class="menu-item" href="/blogs">blogs</a>
        
        <span class="menu-item">&nbsp;|&nbsp;</span>
        
      </li>

      
      <li class="menu-item">
        <a class="menu-item" href="http://localhost:1313/blogs/second/">next &gt;</a>
      </li>
      
      
    </ul>
  </nav>
  
  <h4 class="text-center mt-3"><a class="menu-item" href="http://localhost:1313/">home</a></h4>
</section>

<footer class="text-center footer">
  <hr />
  
  <h6 class="text-center copyright">© 2024. SLY. <a href="http://creativecommons.org/licenses/by/3.0/">Some Rights Reserved</a>.</h6>
  
  <h6 class="text-center powered"><a href="https://gohugo.io/">Hugo</a> <a
      href="https://github.com/shenoydotme/hugo-goa">Goa</a> by <a href="https://shenoy.me">shenoydotme</a> and <a
      href="https://incirclemedia.com">Incircle Media</a></h6>
  
  
  <h6>
    <a href="http://localhost:1313/index.xml" aria-label="RSS Feed"><i class="fas fa-rss" aria-hidden="true"></i></a>
  </h6>
  
  

</footer>
</div>



<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>


<script>hljs.highlightAll();</script>




<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
  integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM" crossorigin="anonymous"></script>
<script src="/js/custom.js"></script>
</body>

</html>

